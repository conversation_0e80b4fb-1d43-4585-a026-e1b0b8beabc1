// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User Management
model User {
  id              String    @id @default(uuid())
  email           String    @unique
  password        String
  role            String    @default("user") // "user" | "admin"
  kycStatus       String    @default("pending") // "pending" | "approved" | "rejected"
  fiatBalance     Float     @default(0)
  cryptoHoldings  String    @default("{}") // JSON as string for SQLite
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  transactions    Transaction[]

  @@index([email])
  @@index([kycStatus])
}

model Transaction {
  id        String   @id @default(uuid())
  type      String   // "fiat-deposit" | "crypto-buy" | "wire" | "swift" etc
  amount    Float
  userId    String
  status    String   // "pending" | "completed" | "failed"
  metadata  String   @default("{}") // JSON as string for SQLite
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
}


