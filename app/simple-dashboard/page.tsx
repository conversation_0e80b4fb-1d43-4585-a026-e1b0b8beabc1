'use client'

import { useEffect, useState } from 'react'

export default function SimpleDashboard() {
  const [token, setToken] = useState<string | null>(null)
  
  useEffect(() => {
    const storedToken = localStorage.getItem('token')
    setToken(storedToken)
    console.log('Simple dashboard loaded, token:', storedToken ? 'exists' : 'missing')
  }, [])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="glass-card p-8 rounded-3xl max-w-md w-full text-center">
        <h1 className="text-4xl font-bold gradient-text mb-4">
          Simple Dashboard
        </h1>
        <p className="text-white/80 mb-4">
          This is a simplified dashboard to test authentication.
        </p>
        <div className="bg-white/10 p-4 rounded-lg">
          <p className="text-white/70 text-sm">
            Token Status: {token ? '✅ Present' : '❌ Missing'}
          </p>
          {token && (
            <p className="text-white/50 text-xs mt-2 break-all">
              {token.substring(0, 50)}...
            </p>
          )}
        </div>
        <button 
          onClick={() => window.location.href = '/auth/login'}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Back to Login
        </button>
      </div>
    </div>
  )
}
