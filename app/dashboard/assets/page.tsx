'use client'

import { useState } from 'react'

export default function AssetsPage() {
  const [activeTab, setActiveTab] = useState('portfolio')
  const [sortBy, setSortBy] = useState('value')

  const assets = [
    {
      symbol: 'BTC',
      name: 'Bitcoin',
      balance: '2.5',
      usdValue: '$125,000',
      price: '$50,000',
      change24h: '+2.5%',
      changePositive: true,
      allocation: 45.2,
      icon: 'fab fa-bitcoin',
      color: 'text-orange-400'
    },
    {
      symbol: 'ETH',
      name: 'Ethereum',
      balance: '15.8',
      usdValue: '$47,400',
      price: '$3,000',
      change24h: '-1.2%',
      changePositive: false,
      allocation: 17.1,
      icon: 'fab fa-ethereum',
      color: 'text-blue-400'
    },
    {
      symbol: 'ADA',
      name: 'Cardano',
      balance: '5,000',
      usdValue: '$2,500',
      price: '$0.50',
      change24h: '+5.8%',
      changePositive: true,
      allocation: 0.9,
      icon: 'fas fa-coins',
      color: 'text-purple-400'
    },
    {
      symbol: 'SOL',
      name: '<PERSON><PERSON>',
      balance: '100',
      usdValue: '$8,500',
      price: '$85',
      change24h: '+12.3%',
      changePositive: true,
      allocation: 3.1,
      icon: 'fas fa-sun',
      color: 'text-indigo-400'
    },
    {
      symbol: 'USD',
      name: 'US Dollar',
      balance: '10,250.00',
      usdValue: '$10,250',
      price: '$1.00',
      change24h: '0.0%',
      changePositive: true,
      allocation: 3.7,
      icon: 'fas fa-dollar-sign',
      color: 'text-green-400'
    }
  ]

  const tokenizedAssets = [
    {
      name: 'Real Estate Token - NYC Apartment',
      symbol: 'RET-NYC-001',
      shares: '50',
      totalShares: '1000',
      value: '$125,000',
      propertyValue: '$2,500,000',
      yield: '4.2%',
      type: 'Real Estate'
    },
    {
      name: 'Gold Token',
      symbol: 'GOLD-T',
      shares: '25',
      totalShares: '10000',
      value: '$50,000',
      propertyValue: '$20,000,000',
      yield: '2.1%',
      type: 'Commodity'
    },
    {
      name: 'Art Token - Picasso Collection',
      symbol: 'ART-PIC-001',
      shares: '10',
      totalShares: '100',
      value: '$500,000',
      propertyValue: '$5,000,000',
      yield: '1.8%',
      type: 'Art'
    }
  ]

  const totalPortfolioValue = assets.reduce((sum, asset) => 
    sum + parseFloat(asset.usdValue.replace('$', '').replace(',', '')), 0
  )

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Assets</h1>
          <p className="text-white/60">Manage your digital and tokenized assets</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-plus mr-2"></i>
            Add Asset
          </button>
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-chart-pie mr-2"></i>
            Rebalance
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['portfolio', 'crypto', 'tokenized', 'performance'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Portfolio Overview */}
      {activeTab === 'portfolio' && (
        <div className="space-y-8">
          {/* Portfolio Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-2xl">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Total Portfolio Value</h3>
                <i className="fas fa-chart-line text-white"></i>
              </div>
              <p className="text-3xl font-bold text-white">${totalPortfolioValue.toLocaleString()}</p>
              <p className="text-green-400 text-sm">+$8,420 (+4.5%) today</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Asset Count</h3>
                <i className="fas fa-layer-group text-blue-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">{assets.length}</p>
              <p className="text-white/60 text-sm">Digital assets</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Best Performer</h3>
                <i className="fas fa-trophy text-yellow-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">SOL</p>
              <p className="text-green-400 text-sm">+12.3% today</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Diversification</h3>
                <i className="fas fa-chart-pie text-purple-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">Good</p>
              <p className="text-white/60 text-sm">5 asset classes</p>
            </div>
          </div>

          {/* Asset Allocation Chart */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Asset Allocation</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Pie Chart Placeholder */}
              <div className="h-64 bg-white/5 rounded-xl flex items-center justify-center">
                <div className="text-center">
                  <i className="fas fa-chart-pie text-4xl text-white/40 mb-4"></i>
                  <p className="text-white/60">Asset allocation chart</p>
                  <p className="text-white/40 text-sm">Chart.js integration coming soon</p>
                </div>
              </div>
              
              {/* Allocation Breakdown */}
              <div className="space-y-4">
                {assets.map((asset) => (
                  <div key={asset.symbol} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <i className={`${asset.icon} ${asset.color} text-xl mr-3`}></i>
                      <div>
                        <p className="text-white font-medium">{asset.name}</p>
                        <p className="text-white/60 text-sm">{asset.symbol}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium">{asset.allocation}%</p>
                      <p className="text-white/60 text-sm">{asset.usdValue}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Assets Table */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl overflow-hidden">
            <div className="p-6 border-b border-white/10 flex justify-between items-center">
              <h2 className="text-xl font-bold text-white">Your Assets</h2>
              <select 
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white text-sm"
              >
                <option value="value" className="bg-gray-800">Sort by Value</option>
                <option value="change" className="bg-gray-800">Sort by Change</option>
                <option value="name" className="bg-gray-800">Sort by Name</option>
              </select>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/5">
                  <tr>
                    <th className="text-left p-4 text-white/70 font-medium">Asset</th>
                    <th className="text-left p-4 text-white/70 font-medium">Balance</th>
                    <th className="text-left p-4 text-white/70 font-medium">Price</th>
                    <th className="text-left p-4 text-white/70 font-medium">24h Change</th>
                    <th className="text-left p-4 text-white/70 font-medium">Value</th>
                    <th className="text-left p-4 text-white/70 font-medium">Allocation</th>
                    <th className="text-left p-4 text-white/70 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {assets.map((asset) => (
                    <tr key={asset.symbol} className="border-b border-white/5 hover:bg-white/5 transition-colors">
                      <td className="p-4">
                        <div className="flex items-center">
                          <i className={`${asset.icon} ${asset.color} text-xl mr-3`}></i>
                          <div>
                            <p className="text-white font-medium">{asset.name}</p>
                            <p className="text-white/60 text-sm">{asset.symbol}</p>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <p className="text-white">{asset.balance} {asset.symbol}</p>
                      </td>
                      <td className="p-4">
                        <p className="text-white">{asset.price}</p>
                      </td>
                      <td className="p-4">
                        <span className={`font-medium ${asset.changePositive ? 'text-green-400' : 'text-red-400'}`}>
                          {asset.change24h}
                        </span>
                      </td>
                      <td className="p-4">
                        <p className="text-white font-medium">{asset.usdValue}</p>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center">
                          <div className="w-16 bg-white/10 rounded-full h-2 mr-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${asset.allocation}%` }}
                            ></div>
                          </div>
                          <span className="text-white text-sm">{asset.allocation}%</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <button className="text-blue-400 hover:text-blue-300">
                            <i className="fas fa-paper-plane"></i>
                          </button>
                          <button className="text-green-400 hover:text-green-300">
                            <i className="fas fa-plus"></i>
                          </button>
                          <button className="text-orange-400 hover:text-orange-300">
                            <i className="fas fa-exchange-alt"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Tokenized Assets Tab */}
      {activeTab === 'tokenized' && (
        <div className="space-y-8">
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Tokenized Assets</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tokenizedAssets.map((asset, index) => (
                <div key={index} className="bg-white/5 rounded-xl p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-white font-semibold">{asset.name}</h3>
                      <p className="text-white/60 text-sm">{asset.symbol}</p>
                    </div>
                    <span className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">
                      {asset.type}
                    </span>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-white/70 text-sm">Your Shares</span>
                      <span className="text-white">{asset.shares}/{asset.totalShares}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70 text-sm">Your Value</span>
                      <span className="text-white font-medium">{asset.value}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70 text-sm">Annual Yield</span>
                      <span className="text-green-400">{asset.yield}</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-blue-600 hover:bg-blue-700 py-2 rounded-lg text-white text-sm transition-colors">
                        Trade
                      </button>
                      <button className="flex-1 bg-white/10 hover:bg-white/20 py-2 rounded-lg text-white text-sm transition-colors">
                        Details
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
