'use client'

import { useState } from 'react'

export default function StakingPage() {
  const [activeTab, setActiveTab] = useState('overview')

  const stakingPools = [
    {
      asset: 'ETH',
      name: 'Ethereum 2.0',
      apy: '5.2%',
      minStake: '0.1 ETH',
      totalStaked: '$2.4B',
      yourStake: '5.0 ETH',
      rewards: '0.25 ETH',
      status: 'active',
      lockPeriod: 'Flexible',
      icon: 'fab fa-ethereum',
      color: 'text-blue-400'
    },
    {
      asset: 'ADA',
      name: 'Cardano Staking',
      apy: '4.8%',
      minStake: '10 ADA',
      totalStaked: '$1.2B',
      yourStake: '1,000 ADA',
      rewards: '48 ADA',
      status: 'active',
      lockPeriod: '5 days',
      icon: 'fas fa-coins',
      color: 'text-purple-400'
    },
    {
      asset: 'SOL',
      name: 'Solana Staking',
      apy: '7.1%',
      minStake: '1 SOL',
      totalStaked: '$800M',
      yourStake: '50 SOL',
      rewards: '3.55 SOL',
      status: 'active',
      lockPeriod: '2-3 days',
      icon: 'fas fa-sun',
      color: 'text-indigo-400'
    },
    {
      asset: 'DOT',
      name: 'Polkadot Staking',
      apy: '12.5%',
      minStake: '1 DOT',
      totalStaked: '$500M',
      yourStake: '0 DOT',
      rewards: '0 DOT',
      status: 'available',
      lockPeriod: '28 days',
      icon: 'fas fa-circle',
      color: 'text-pink-400'
    }
  ]

  const stakingHistory = [
    {
      date: '2024-12-28',
      action: 'Reward Claimed',
      asset: 'ETH',
      amount: '+0.05 ETH',
      value: '+$150'
    },
    {
      date: '2024-12-25',
      action: 'Staked',
      asset: 'SOL',
      amount: '+25 SOL',
      value: '+$2,125'
    },
    {
      date: '2024-12-20',
      action: 'Unstaked',
      asset: 'ADA',
      amount: '-500 ADA',
      value: '-$250'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Staking</h1>
          <p className="text-white/60">Earn rewards by staking your crypto assets</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-plus mr-2"></i>
            Stake Assets
          </button>
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-gift mr-2"></i>
            Claim Rewards
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['overview', 'pools', 'history', 'rewards'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* Staking Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Total Staked</h3>
                <i className="fas fa-coins text-blue-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$18,750</p>
              <p className="text-green-400 text-sm">3 active stakes</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Total Rewards</h3>
                <i className="fas fa-gift text-green-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$1,247</p>
              <p className="text-green-400 text-sm">+$125 this month</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Average APY</h3>
                <i className="fas fa-percentage text-purple-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">5.7%</p>
              <p className="text-white/60 text-sm">Weighted average</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Pending Rewards</h3>
                <i className="fas fa-clock text-orange-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$89</p>
              <p className="text-white/60 text-sm">Ready to claim</p>
            </div>
          </div>

          {/* Active Stakes */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Your Active Stakes</h2>
            <div className="space-y-4">
              {stakingPools.filter(pool => pool.status === 'active').map((pool) => (
                <div key={pool.asset} className="bg-white/5 rounded-xl p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center">
                      <i className={`${pool.icon} ${pool.color} text-2xl mr-4`}></i>
                      <div>
                        <h3 className="text-white font-semibold text-lg">{pool.name}</h3>
                        <p className="text-white/60">{pool.asset} • APY: {pool.apy}</p>
                      </div>
                    </div>
                    <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-lg text-sm font-medium">
                      Active
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-white/70 text-sm">Your Stake</p>
                      <p className="text-white font-semibold">{pool.yourStake}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Rewards Earned</p>
                      <p className="text-green-400 font-semibold">{pool.rewards}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Lock Period</p>
                      <p className="text-white font-semibold">{pool.lockPeriod}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">APY</p>
                      <p className="text-purple-400 font-semibold">{pool.apy}</p>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 mt-4">
                    <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                      Add More
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                      Claim Rewards
                    </button>
                    <button className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                      Unstake
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Pools Tab */}
      {activeTab === 'pools' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Available Staking Pools</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {stakingPools.map((pool) => (
              <div key={pool.asset} className="bg-white/5 rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <i className={`${pool.icon} ${pool.color} text-2xl mr-3`}></i>
                    <div>
                      <h3 className="text-white font-semibold">{pool.name}</h3>
                      <p className="text-white/60 text-sm">{pool.asset}</p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-lg text-sm font-medium ${
                    pool.status === 'active' 
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-blue-500/20 text-blue-400'
                  }`}>
                    {pool.status}
                  </span>
                </div>
                
                <div className="space-y-3 mb-4">
                  <div className="flex justify-between">
                    <span className="text-white/70 text-sm">APY</span>
                    <span className="text-purple-400 font-semibold">{pool.apy}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70 text-sm">Min Stake</span>
                    <span className="text-white">{pool.minStake}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70 text-sm">Lock Period</span>
                    <span className="text-white">{pool.lockPeriod}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70 text-sm">Total Staked</span>
                    <span className="text-white">{pool.totalStaked}</span>
                  </div>
                </div>
                
                <button className={`w-full py-2 rounded-lg text-white text-sm transition-colors ${
                  pool.status === 'active' 
                    ? 'bg-green-600 hover:bg-green-700' 
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}>
                  {pool.status === 'active' ? 'Manage Stake' : 'Start Staking'}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* History Tab */}
      {activeTab === 'history' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Staking History</h2>
          <div className="space-y-4">
            {stakingHistory.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                    <i className="fas fa-coins text-white"></i>
                  </div>
                  <div>
                    <p className="text-white font-medium">{item.action}</p>
                    <p className="text-white/60 text-sm">{item.date} • {item.asset}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-medium">{item.amount}</p>
                  <p className="text-white/60 text-sm">{item.value}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
