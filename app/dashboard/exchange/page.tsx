'use client'

import { useState } from 'react'

export default function ExchangePage() {
  const [fromAsset, setFromAsset] = useState('BTC')
  const [toAsset, setToAsset] = useState('ETH')
  const [amount, setAmount] = useState('')
  const [orderType, setOrderType] = useState('market')

  const assets = [
    { symbol: 'BTC', name: 'Bitcoin', price: '$50,000', change: '+2.5%', icon: 'fab fa-bitcoin', color: 'text-orange-400' },
    { symbol: 'ETH', name: 'Ethereum', price: '$3,000', change: '-1.2%', icon: 'fab fa-ethereum', color: 'text-blue-400' },
    { symbol: 'ADA', name: 'Cardano', price: '$0.50', change: '+5.8%', icon: 'fas fa-coins', color: 'text-purple-400' },
    { symbol: 'S<PERSON>', name: '<PERSON><PERSON>', price: '$85', change: '+12.3%', icon: 'fas fa-sun', color: 'text-indigo-400' },
    { symbol: 'DOT', name: '<PERSON><PERSON><PERSON>', price: '$7.50', change: '+3.1%', icon: 'fas fa-circle', color: 'text-pink-400' },
    { symbol: 'LINK', name: 'Chainlink', price: '$15.20', change: '-0.8%', icon: 'fas fa-link', color: 'text-blue-500' },
  ]

  const recentTrades = [
    { pair: 'BTC/ETH', type: 'Buy', amount: '0.5 BTC', price: '$49,800', time: '2 min ago', status: 'Completed' },
    { pair: 'ETH/ADA', type: 'Sell', amount: '2.0 ETH', price: '$2,980', time: '15 min ago', status: 'Completed' },
    { pair: 'SOL/USD', type: 'Buy', amount: '10 SOL', price: '$84.50', time: '1 hour ago', status: 'Pending' },
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Exchange</h1>
          <p className="text-white/60">Trade cryptocurrencies with advanced tools</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-chart-line mr-2"></i>
            Advanced Trading
          </button>
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-history mr-2"></i>
            Order History
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Trading Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Trade</h2>
            
            {/* Order Type Tabs */}
            <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
              {['market', 'limit', 'stop'].map((type) => (
                <button
                  key={type}
                  onClick={() => setOrderType(type)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
                    orderType === type
                      ? 'bg-blue-600 text-white'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>

            {/* From Asset */}
            <div className="mb-4">
              <label className="block text-white/70 text-sm mb-2">From</label>
              <div className="bg-white/10 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <select 
                    value={fromAsset}
                    onChange={(e) => setFromAsset(e.target.value)}
                    className="bg-transparent text-white text-lg font-semibold outline-none"
                  >
                    {assets.map(asset => (
                      <option key={asset.symbol} value={asset.symbol} className="bg-gray-800">
                        {asset.symbol}
                      </option>
                    ))}
                  </select>
                  <span className="text-white/60 text-sm">Balance: 2.5</span>
                </div>
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="0.00"
                  className="w-full bg-transparent text-white text-2xl font-bold outline-none"
                />
              </div>
            </div>

            {/* Swap Button */}
            <div className="flex justify-center mb-4">
              <button className="w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors">
                <i className="fas fa-exchange-alt text-white"></i>
              </button>
            </div>

            {/* To Asset */}
            <div className="mb-6">
              <label className="block text-white/70 text-sm mb-2">To</label>
              <div className="bg-white/10 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <select 
                    value={toAsset}
                    onChange={(e) => setToAsset(e.target.value)}
                    className="bg-transparent text-white text-lg font-semibold outline-none"
                  >
                    {assets.map(asset => (
                      <option key={asset.symbol} value={asset.symbol} className="bg-gray-800">
                        {asset.symbol}
                      </option>
                    ))}
                  </select>
                  <span className="text-white/60 text-sm">Balance: 15.8</span>
                </div>
                <div className="text-white text-2xl font-bold">
                  {amount ? (parseFloat(amount) * 16.67).toFixed(4) : '0.00'}
                </div>
              </div>
            </div>

            {/* Trade Info */}
            <div className="space-y-2 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-white/70">Rate</span>
                <span className="text-white">1 BTC = 16.67 ETH</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-white/70">Fee</span>
                <span className="text-white">0.1%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-white/70">Estimated Total</span>
                <span className="text-white font-semibold">
                  {amount ? (parseFloat(amount) * 16.67 * 0.999).toFixed(4) : '0.00'} ETH
                </span>
              </div>
            </div>

            {/* Trade Button */}
            <button className="w-full bg-blue-600 hover:bg-blue-700 py-3 rounded-xl text-white font-semibold transition-colors">
              <i className="fas fa-exchange-alt mr-2"></i>
              Execute Trade
            </button>
          </div>
        </div>

        {/* Market Data */}
        <div className="lg:col-span-2">
          {/* Market Overview */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 mb-6">
            <h2 className="text-xl font-bold text-white mb-6">Market Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {assets.map((asset) => (
                <div key={asset.symbol} className="bg-white/5 rounded-xl p-4 hover:bg-white/10 transition-colors cursor-pointer">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <i className={`${asset.icon} ${asset.color} text-xl mr-3`}></i>
                      <div>
                        <p className="text-white font-semibold">{asset.symbol}</p>
                        <p className="text-white/60 text-sm">{asset.name}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white font-bold">{asset.price}</span>
                    <span className={`text-sm font-medium ${
                      asset.change.startsWith('+') ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {asset.change}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Trades */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Recent Trades</h2>
            <div className="space-y-4">
              {recentTrades.map((trade, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-3 ${
                      trade.type === 'Buy' ? 'bg-green-500' : 'bg-red-500'
                    }`}>
                      <i className={`fas ${trade.type === 'Buy' ? 'fa-arrow-up' : 'fa-arrow-down'} text-white`}></i>
                    </div>
                    <div>
                      <p className="text-white font-medium">{trade.pair}</p>
                      <p className="text-white/60 text-sm">{trade.type} • {trade.time}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">{trade.amount}</p>
                    <p className="text-white/60 text-sm">{trade.price}</p>
                  </div>
                  <div className="text-right">
                    <span className={`px-2 py-1 rounded-lg text-xs font-medium ${
                      trade.status === 'Completed' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {trade.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
