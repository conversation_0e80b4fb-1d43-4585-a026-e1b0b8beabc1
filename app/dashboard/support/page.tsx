'use client'

import { useState } from 'react'

export default function SupportPage() {
  const [activeTab, setActiveTab] = useState('tickets')
  const [ticketSubject, setTicketSubject] = useState('')
  const [ticketMessage, setTicketMessage] = useState('')

  const tickets = [
    {
      id: 'TICK-001',
      subject: 'Unable to withdraw funds',
      status: 'open',
      priority: 'high',
      created: '2024-12-28',
      lastUpdate: '2024-12-28',
      category: 'Technical'
    },
    {
      id: 'TICK-002',
      subject: 'Question about staking rewards',
      status: 'resolved',
      priority: 'medium',
      created: '2024-12-25',
      lastUpdate: '2024-12-26',
      category: 'General'
    },
    {
      id: 'TICK-003',
      subject: 'Account verification issue',
      status: 'in_progress',
      priority: 'high',
      created: '2024-12-20',
      lastUpdate: '2024-12-27',
      category: 'Account'
    }
  ]

  const faqs = [
    {
      question: 'How do I deposit funds into my account?',
      answer: 'You can deposit funds by going to the Wallets section and clicking on the deposit button for your desired currency. Follow the instructions to complete the deposit.',
      category: 'Deposits'
    },
    {
      question: 'What are the withdrawal limits?',
      answer: 'Withdrawal limits depend on your account verification level. Basic accounts have a $10,000 daily limit, while verified accounts have higher limits.',
      category: 'Withdrawals'
    },
    {
      question: 'How does staking work?',
      answer: 'Staking allows you to earn rewards by holding certain cryptocurrencies. Visit the Staking section to see available pools and their respective APY rates.',
      category: 'Staking'
    },
    {
      question: 'Is my account secure?',
      answer: 'Yes, we use bank-grade security including 2FA, cold storage for funds, and regular security audits. You can review your security settings in the Security section.',
      category: 'Security'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-500/20 text-blue-400'
      case 'in_progress': return 'bg-yellow-500/20 text-yellow-400'
      case 'resolved': return 'bg-green-500/20 text-green-400'
      case 'closed': return 'bg-gray-500/20 text-gray-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Support</h1>
          <p className="text-white/60">Get help and manage your support tickets</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-plus mr-2"></i>
            New Ticket
          </button>
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-comments mr-2"></i>
            Live Chat
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['tickets', 'faq', 'contact', 'resources'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab === 'faq' ? 'FAQ' : tab}
          </button>
        ))}
      </div>

      {/* Tickets Tab */}
      {activeTab === 'tickets' && (
        <div className="space-y-8">
          {/* Support Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Open Tickets</h3>
                <i className="fas fa-ticket-alt text-blue-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">2</p>
              <p className="text-white/60 text-sm">Active support requests</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Avg Response Time</h3>
                <i className="fas fa-clock text-green-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">2.5h</p>
              <p className="text-green-400 text-sm">Faster than average</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Resolved Tickets</h3>
                <i className="fas fa-check-circle text-purple-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">15</p>
              <p className="text-white/60 text-sm">This month</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Satisfaction</h3>
                <i className="fas fa-star text-yellow-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">4.8/5</p>
              <p className="text-white/60 text-sm">Average rating</p>
            </div>
          </div>

          {/* Tickets List */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl overflow-hidden">
            <div className="p-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white">Your Support Tickets</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/5">
                  <tr>
                    <th className="text-left p-4 text-white/70 font-medium">Ticket ID</th>
                    <th className="text-left p-4 text-white/70 font-medium">Subject</th>
                    <th className="text-left p-4 text-white/70 font-medium">Status</th>
                    <th className="text-left p-4 text-white/70 font-medium">Priority</th>
                    <th className="text-left p-4 text-white/70 font-medium">Created</th>
                    <th className="text-left p-4 text-white/70 font-medium">Last Update</th>
                    <th className="text-left p-4 text-white/70 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {tickets.map((ticket) => (
                    <tr key={ticket.id} className="border-b border-white/5 hover:bg-white/5 transition-colors">
                      <td className="p-4">
                        <span className="text-blue-400 font-medium">{ticket.id}</span>
                      </td>
                      <td className="p-4">
                        <p className="text-white">{ticket.subject}</p>
                        <p className="text-white/60 text-sm">{ticket.category}</p>
                      </td>
                      <td className="p-4">
                        <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(ticket.status)}`}>
                          {ticket.status.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className={`font-medium ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{ticket.created}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{ticket.lastUpdate}</span>
                      </td>
                      <td className="p-4">
                        <button className="text-blue-400 hover:text-blue-300 mr-2">
                          <i className="fas fa-eye"></i>
                        </button>
                        <button className="text-green-400 hover:text-green-300">
                          <i className="fas fa-reply"></i>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* FAQ Tab */}
      {activeTab === 'faq' && (
        <div className="space-y-6">
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Frequently Asked Questions</h2>
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <div key={index} className="bg-white/5 rounded-xl p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-white font-medium">{faq.question}</h3>
                    <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">
                      {faq.category}
                    </span>
                  </div>
                  <p className="text-white/70 text-sm">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Contact Tab */}
      {activeTab === 'contact' && (
        <div className="space-y-8">
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Contact Support</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-white/70 text-sm mb-2">Subject</label>
                <input
                  type="text"
                  value={ticketSubject}
                  onChange={(e) => setTicketSubject(e.target.value)}
                  placeholder="Brief description of your issue"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-white/70 text-sm mb-2">Category</label>
                <select className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500">
                  <option value="technical" className="bg-gray-800">Technical Issue</option>
                  <option value="account" className="bg-gray-800">Account Related</option>
                  <option value="trading" className="bg-gray-800">Trading</option>
                  <option value="general" className="bg-gray-800">General Question</option>
                </select>
              </div>
              
              <div>
                <label className="block text-white/70 text-sm mb-2">Priority</label>
                <select className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500">
                  <option value="low" className="bg-gray-800">Low</option>
                  <option value="medium" className="bg-gray-800">Medium</option>
                  <option value="high" className="bg-gray-800">High</option>
                  <option value="urgent" className="bg-gray-800">Urgent</option>
                </select>
              </div>
              
              <div>
                <label className="block text-white/70 text-sm mb-2">Message</label>
                <textarea
                  value={ticketMessage}
                  onChange={(e) => setTicketMessage(e.target.value)}
                  placeholder="Describe your issue in detail..."
                  rows={6}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-500 resize-none"
                />
              </div>
              
              <button className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg text-white transition-colors">
                <i className="fas fa-paper-plane mr-2"></i>
                Submit Ticket
              </button>
            </div>
          </div>

          {/* Contact Methods */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 text-center">
              <i className="fas fa-comments text-3xl text-blue-400 mb-4"></i>
              <h3 className="text-white font-semibold mb-2">Live Chat</h3>
              <p className="text-white/60 text-sm mb-4">Get instant help from our support team</p>
              <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                Start Chat
              </button>
            </div>
            
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 text-center">
              <i className="fas fa-envelope text-3xl text-green-400 mb-4"></i>
              <h3 className="text-white font-semibold mb-2">Email Support</h3>
              <p className="text-white/60 text-sm mb-4">Send us an email for detailed assistance</p>
              <p className="text-green-400 text-sm"><EMAIL></p>
            </div>
            
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 text-center">
              <i className="fas fa-phone text-3xl text-purple-400 mb-4"></i>
              <h3 className="text-white font-semibold mb-2">Phone Support</h3>
              <p className="text-white/60 text-sm mb-4">Call us for urgent matters</p>
              <p className="text-purple-400 text-sm">+****************</p>
            </div>
          </div>
        </div>
      )}

      {/* Resources Tab */}
      {activeTab === 'resources' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Help Resources</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-white font-semibold">Documentation</h3>
              <div className="space-y-2">
                <a href="#" className="block text-blue-400 hover:text-blue-300 text-sm">
                  <i className="fas fa-book mr-2"></i>
                  Getting Started Guide
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300 text-sm">
                  <i className="fas fa-chart-line mr-2"></i>
                  Trading Tutorial
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300 text-sm">
                  <i className="fas fa-shield-alt mr-2"></i>
                  Security Best Practices
                </a>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-white font-semibold">Video Tutorials</h3>
              <div className="space-y-2">
                <a href="#" className="block text-blue-400 hover:text-blue-300 text-sm">
                  <i className="fas fa-play mr-2"></i>
                  How to Make Your First Trade
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300 text-sm">
                  <i className="fas fa-play mr-2"></i>
                  Setting Up 2FA
                </a>
                <a href="#" className="block text-blue-400 hover:text-blue-300 text-sm">
                  <i className="fas fa-play mr-2"></i>
                  Understanding Staking
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
