'use client'

import { useState } from 'react'

export default function CreditPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [creditAmount, setCreditAmount] = useState('')
  const [showApplication, setShowApplication] = useState(false)

  const creditLines = [
    {
      id: 'CL001',
      name: 'Premium Credit Line',
      limit: '$100,000',
      available: '$75,000',
      used: '$25,000',
      interestRate: '12.5%',
      monthlyPayment: '$312',
      nextPayment: '2025-01-15',
      status: 'active',
      collateral: '5.0 BTC',
      utilization: 25
    },
    {
      id: 'CL002',
      name: 'Business Credit Line',
      limit: '$50,000',
      available: '$50,000',
      used: '$0',
      interestRate: '14.2%',
      monthlyPayment: '$0',
      nextPayment: 'N/A',
      status: 'active',
      collateral: '20 ETH',
      utilization: 0
    }
  ]

  const transactions = [
    {
      id: 'CR001',
      type: 'withdrawal',
      amount: '$5,000',
      date: '2024-12-28',
      description: 'Cash advance',
      balance: '$25,000'
    },
    {
      id: 'CR002',
      type: 'payment',
      amount: '$1,000',
      date: '2024-12-25',
      description: 'Monthly payment',
      balance: '$20,000'
    },
    {
      id: 'CR003',
      type: 'withdrawal',
      amount: '$15,000',
      date: '2024-12-20',
      description: 'Business expense',
      balance: '$21,000'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Credit Lines</h1>
          <p className="text-white/60">Manage your crypto-backed lines of credit</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setShowApplication(true)}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors"
          >
            <i className="fas fa-plus mr-2"></i>
            Apply for Credit Line
          </button>
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-credit-card mr-2"></i>
            Request Card
          </button>
        </div>
      </div>

      {/* Credit Application Modal */}
      {showApplication && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-2xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white">Apply for Credit Line</h2>
              <button 
                onClick={() => setShowApplication(false)}
                className="text-white/60 hover:text-white"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-white/70 text-sm mb-2">Requested Credit Limit</label>
                <input
                  type="number"
                  value={creditAmount}
                  onChange={(e) => setCreditAmount(e.target.value)}
                  placeholder="Enter amount"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">Purpose</label>
                <select className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500">
                  <option value="personal" className="bg-gray-800">Personal Use</option>
                  <option value="business" className="bg-gray-800">Business Expenses</option>
                  <option value="investment" className="bg-gray-800">Investment</option>
                  <option value="emergency" className="bg-gray-800">Emergency Fund</option>
                </select>
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">Collateral Type</label>
                <select className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500">
                  <option value="BTC" className="bg-gray-800">Bitcoin (BTC)</option>
                  <option value="ETH" className="bg-gray-800">Ethereum (ETH)</option>
                  <option value="mixed" className="bg-gray-800">Mixed Portfolio</option>
                </select>
              </div>

              <div className="flex space-x-3">
                <button 
                  onClick={() => setShowApplication(false)}
                  className="flex-1 bg-white/10 hover:bg-white/20 py-3 rounded-lg text-white transition-colors"
                >
                  Cancel
                </button>
                <button className="flex-1 bg-blue-600 hover:bg-blue-700 py-3 rounded-lg text-white transition-colors">
                  Submit Application
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['overview', 'active', 'transactions', 'payments'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* Credit Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Total Credit Limit</h3>
                <i className="fas fa-credit-card text-blue-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$150,000</p>
              <p className="text-green-400 text-sm">2 active lines</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Available Credit</h3>
                <i className="fas fa-wallet text-green-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$125,000</p>
              <p className="text-white/60 text-sm">83.3% available</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Used Credit</h3>
                <i className="fas fa-chart-pie text-orange-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$25,000</p>
              <p className="text-white/60 text-sm">16.7% utilization</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Monthly Payment</h3>
                <i className="fas fa-calendar-alt text-purple-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$312</p>
              <p className="text-white/60 text-sm">Due Jan 15, 2025</p>
            </div>
          </div>

          {/* Active Credit Lines */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Active Credit Lines</h2>
            <div className="space-y-6">
              {creditLines.map((line) => (
                <div key={line.id} className="bg-white/5 rounded-xl p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-white font-semibold text-lg">{line.name}</h3>
                      <p className="text-white/60">{line.id} • Collateral: {line.collateral}</p>
                    </div>
                    <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-lg text-sm font-medium">
                      {line.status}
                    </span>
                  </div>
                  
                  {/* Credit Utilization Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-white/70">Credit Utilization</span>
                      <span className="text-white">{line.utilization}%</span>
                    </div>
                    <div className="w-full bg-white/10 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${line.utilization}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-white/70 text-sm">Credit Limit</p>
                      <p className="text-white font-semibold">{line.limit}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Available</p>
                      <p className="text-green-400 font-semibold">{line.available}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Used</p>
                      <p className="text-orange-400 font-semibold">{line.used}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Interest Rate</p>
                      <p className="text-white font-semibold">{line.interestRate}</p>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center mt-4">
                    <div>
                      <p className="text-white/70 text-sm">Next Payment: {line.nextPayment}</p>
                      <p className="text-white/70 text-sm">Monthly Payment: {line.monthlyPayment}</p>
                    </div>
                    <div className="flex space-x-2">
                      <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                        Withdraw
                      </button>
                      <button className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                        Make Payment
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Transactions Tab */}
      {activeTab === 'transactions' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Credit Line Transactions</h2>
          <div className="space-y-4">
            {transactions.map((tx) => (
              <div key={tx.id} className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                <div className="flex items-center">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-3 ${
                    tx.type === 'payment' ? 'bg-green-500' : 'bg-blue-500'
                  }`}>
                    <i className={`fas ${
                      tx.type === 'payment' ? 'fa-arrow-down' : 'fa-arrow-up'
                    } text-white`}></i>
                  </div>
                  <div>
                    <p className="text-white font-medium">{tx.description}</p>
                    <p className="text-white/60 text-sm">{tx.date} • {tx.id}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-medium ${
                    tx.type === 'payment' ? 'text-green-400' : 'text-blue-400'
                  }`}>
                    {tx.type === 'payment' ? '+' : ''}{tx.amount}
                  </p>
                  <p className="text-white/60 text-sm">Balance: {tx.balance}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
