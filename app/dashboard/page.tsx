'use client'

export default function DashboardPage() {
  return (
    <div className="p-6">
      {/* Dashboard Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
          <p className="text-white/60">Welcome back, <PERSON></p>
        </div>
        <div className="flex items-center space-x-4">
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-bell mr-2"></i>
            Notifications
          </button>
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Home
          </button>
        </div>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Balance */}
        <div className="bg-gradient-to-br from-purple-600 to-pink-600 p-6 rounded-2xl text-white shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/90 text-sm font-medium">Total Balance</h3>
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
              <i className="fas fa-wallet text-white text-lg"></i>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$42,567.89</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-up text-green-300 mr-1"></i>
            <span className="text-green-300">+5.3% this month</span>
          </div>
        </div>

        {/* Crypto Assets */}
        <div className="bg-gradient-to-br from-cyan-500 to-blue-600 p-6 rounded-2xl text-white shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/90 text-sm font-medium">Crypto Assets</h3>
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
              <i className="fab fa-bitcoin text-white text-lg"></i>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$28,350.75</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-up text-green-300 mr-1"></i>
            <span className="text-green-300">+12.8% this month</span>
          </div>
        </div>

        {/* Fiat Balance */}
        <div className="bg-gradient-to-br from-orange-500 to-pink-500 p-6 rounded-2xl text-white shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/90 text-sm font-medium">Fiat Balance</h3>
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
              <i className="fas fa-dollar-sign text-white text-lg"></i>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$10,250.00</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-down text-red-300 mr-1"></i>
            <span className="text-red-300">-2.1% this month</span>
          </div>
        </div>

        {/* Tokenized Assets */}
        <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-6 rounded-2xl text-white shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/90 text-sm font-medium">Tokenized Assets</h3>
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
              <i className="fas fa-coins text-white text-lg"></i>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$3,967.14</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-up text-green-300 mr-1"></i>
            <span className="text-green-300">+3.7% this month</span>
          </div>
        </div>
      </div>

      {/* Portfolio Performance Chart */}
      <div className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 backdrop-blur-lg rounded-2xl p-6 mb-8 border border-white/10">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">Portfolio Performance</h2>
          <div className="flex space-x-2">
            <button className="px-3 py-1 bg-white/10 rounded-lg text-white text-sm hover:bg-white/20 transition-colors">1W</button>
            <button className="px-3 py-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg text-white text-sm">1M</button>
            <button className="px-3 py-1 bg-white/10 rounded-lg text-white text-sm hover:bg-white/20 transition-colors">3M</button>
            <button className="px-3 py-1 bg-white/10 rounded-lg text-white text-sm hover:bg-white/20 transition-colors">1Y</button>
          </div>
        </div>

        {/* Chart Placeholder */}
        <div className="h-64 bg-white/5 rounded-xl flex items-center justify-center">
          <div className="text-center">
            <i className="fas fa-chart-line text-4xl text-white/40 mb-4"></i>
            <p className="text-white/60">Portfolio performance chart will be displayed here</p>
            <p className="text-white/40 text-sm">Integration with Chart.js coming soon</p>
          </div>
        </div>

        {/* Chart Legend */}
        <div className="flex items-center justify-center space-x-6 mt-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span className="text-white/80 text-sm">Total Balance</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-pink-500 rounded-full mr-2"></div>
            <span className="text-white/80 text-sm">Crypto Assets</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-cyan-500 rounded-full mr-2"></div>
            <span className="text-white/80 text-sm">Tokenized Assets</span>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Your Wallets */}
        <div className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Your Wallets</h2>
            <button className="text-cyan-400 hover:text-cyan-300 text-sm font-medium">View All</button>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-orange-500/20 to-pink-500/20 rounded-xl border border-orange-500/30">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-pink-500 rounded-xl flex items-center justify-center mr-3">
                  <i className="fab fa-bitcoin text-white text-lg"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Bitcoin Wallet</p>
                  <p className="text-white/70 text-sm">2.5 BTC</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-bold">$125,000</p>
                <p className="text-green-400 text-sm font-medium">+5.2%</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-xl border border-cyan-500/30">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mr-3">
                  <i className="fab fa-ethereum text-white text-lg"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Ethereum Wallet</p>
                  <p className="text-white/70 text-sm">15.8 ETH</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-bold">$47,400</p>
                <p className="text-red-400 text-sm font-medium">-2.1%</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-xl border border-purple-500/30">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center mr-3">
                  <i className="fas fa-dollar-sign text-white text-lg"></i>
                </div>
                <div>
                  <p className="text-white font-medium">USD Wallet</p>
                  <p className="text-white/70 text-sm">Fiat Currency</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-bold">$10,250</p>
                <p className="text-white/60 text-sm">0.0%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div className="bg-gradient-to-br from-cyan-900/30 to-blue-900/30 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Recent Transactions</h2>
            <button className="text-orange-400 hover:text-orange-300 text-sm font-medium">View All</button>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-500/30">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mr-3">
                  <i className="fas fa-arrow-down text-white text-lg"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Received Bitcoin</p>
                  <p className="text-white/70 text-sm">Dec 28, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-green-400 font-bold">+0.5 BTC</p>
                <p className="text-white/70 text-sm">$25,000</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-red-500/20 to-pink-500/20 rounded-xl border border-red-500/30">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center mr-3">
                  <i className="fas fa-arrow-up text-white text-lg"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Sent Ethereum</p>
                  <p className="text-white/70 text-sm">Dec 27, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-red-400 font-bold">-2.0 ETH</p>
                <p className="text-white/70 text-sm">$6,000</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 rounded-xl border border-blue-500/30">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mr-3">
                  <i className="fas fa-exchange-alt text-white text-lg"></i>
                </div>
                <div>
                  <p className="text-white font-medium">USD Deposit</p>
                  <p className="text-white/70 text-sm">Dec 26, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-green-400 font-bold">+$5,000</p>
                <p className="text-white/70 text-sm">Bank Transfer</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
