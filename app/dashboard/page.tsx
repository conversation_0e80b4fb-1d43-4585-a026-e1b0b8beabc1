'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface User {
  id: string
  email: string
  role: string
  kycStatus: string
  fiatBalance: number
  cryptoHoldings: string
}

export default function DashboardPage() {
  console.log('🎯 Dashboard component rendered')
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    console.log('🔍 Dashboard useEffect triggered')

    // Add a small delay to ensure localStorage is available
    const checkAuth = () => {
      const token = localStorage.getItem('token')
      console.log('🔑 Token from localStorage:', token ? token.substring(0, 20) + '...' : 'null')

      if (!token) {
        console.log('❌ No token found, redirecting to login')
        router.push('/auth/login')
        return
      }

      console.log('✅ Token found, fetching user data')
      fetchUserData(token)
    }

    // Small delay to ensure localStorage is ready
    setTimeout(checkAuth, 100)
  }, [router])

  const fetchUserData = async (token: string) => {
    try {
      console.log('Fetching user data with token:', token.substring(0, 20) + '...')
      const response = await fetch('/api/user/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      console.log('Dashboard API response status:', response.status)

      if (response.ok) {
        const userData = await response.json()
        console.log('User data received:', userData)
        setUser(userData)
      } else {
        const errorData = await response.text()
        console.error('Dashboard API error:', response.status, errorData)
        localStorage.removeItem('token')
        document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
        router.push('/auth/login')
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      localStorage.removeItem('token')
      document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      router.push('/auth/login')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('token')
    // Clear cookie as well
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    router.push('/')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="glass-card p-8 rounded-3xl">
          <i className="fas fa-spinner fa-spin text-4xl text-blue-400 mb-4"></i>
          <p className="text-white/80">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="glass-card p-8 rounded-3xl">
          <p className="text-white/80">No user data available. Redirecting...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-4xl font-bold gradient-text">Dashboard</h1>
          <p className="text-white/70 mt-2">Welcome back, {user.email}</p>
        </div>
        <Button
          onClick={handleLogout}
          variant="outline"
          className="border-white/20 text-white hover:bg-white/10"
        >
          <i className="fas fa-sign-out-alt mr-2"></i>
          Logout
        </Button>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <i className="fas fa-dollar-sign mr-2 text-green-400"></i>
              Fiat Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">
              ${user.fiatBalance.toLocaleString()}
            </p>
            <p className="text-white/60 text-sm mt-2">USD</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <i className="fab fa-bitcoin mr-2 text-orange-400"></i>
              Crypto Holdings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">
              {Object.keys(JSON.parse(user.cryptoHoldings || '{}')).length}
            </p>
            <p className="text-white/60 text-sm mt-2">Assets</p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <i className="fas fa-shield-alt mr-2 text-blue-400"></i>
              KYC Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className={`text-2xl font-bold ${
              user.kycStatus === 'approved' ? 'text-green-400' : 
              user.kycStatus === 'pending' ? 'text-yellow-400' : 'text-red-400'
            }`}>
              {user.kycStatus.charAt(0).toUpperCase() + user.kycStatus.slice(1)}
            </p>
            <p className="text-white/60 text-sm mt-2">Verification</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="glass-card border-white/10">
        <CardHeader>
          <CardTitle className="text-white">Quick Actions</CardTitle>
          <CardDescription className="text-white/70">
            Manage your account and assets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-plus text-2xl mb-2"></i>
              <span>Deposit</span>
            </Button>
            <Button className="bg-purple-600 hover:bg-purple-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-exchange-alt text-2xl mb-2"></i>
              <span>Trade</span>
            </Button>
            <Button className="bg-green-600 hover:bg-green-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-paper-plane text-2xl mb-2"></i>
              <span>Send</span>
            </Button>
            <Button className="bg-orange-600 hover:bg-orange-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-chart-line text-2xl mb-2"></i>
              <span>Analytics</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
