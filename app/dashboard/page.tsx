'use client'

export default function DashboardPage() {
  return (
    <div className="p-6">
      {/* Dashboard Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
          <p className="text-white/60">Welcome back, <PERSON></p>
        </div>
        <div className="flex items-center space-x-4">
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-bell mr-2"></i>
            Notifications
          </button>
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Home
          </button>
        </div>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Balance */}
        <div className="bg-gradient-to-br from-purple-600 to-purple-700 p-6 rounded-2xl text-white">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/80 text-sm">Total Balance</h3>
            <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">$</span>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$42,567.89</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-up text-green-400 mr-1"></i>
            <span className="text-green-400">+5.3% this month</span>
          </div>
        </div>

        {/* Crypto Assets */}
        <div className="bg-gradient-to-br from-pink-600 to-pink-700 p-6 rounded-2xl text-white">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/80 text-sm">Crypto Assets</h3>
            <div className="w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center">
              <i className="fab fa-bitcoin text-white"></i>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$28,350.75</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-up text-green-400 mr-1"></i>
            <span className="text-green-400">+12.8% this month</span>
          </div>
        </div>

        {/* Fiat Balance */}
        <div className="bg-gradient-to-br from-cyan-600 to-cyan-700 p-6 rounded-2xl text-white">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/80 text-sm">Fiat Balance</h3>
            <div className="w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center">
              <i className="fas fa-dollar-sign text-white"></i>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$10,250.00</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-down text-red-400 mr-1"></i>
            <span className="text-red-400">-2.1% this month</span>
          </div>
        </div>

        {/* Tokenized Assets */}
        <div className="bg-gradient-to-br from-orange-600 to-orange-700 p-6 rounded-2xl text-white">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white/80 text-sm">Tokenized Assets</h3>
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <i className="fas fa-coins text-white"></i>
            </div>
          </div>
          <div className="text-3xl font-bold mb-2">$3,967.14</div>
          <div className="flex items-center text-sm">
            <i className="fas fa-arrow-up text-green-400 mr-1"></i>
            <span className="text-green-400">+3.7% this month</span>
          </div>
        </div>
      </div>

      {/* Portfolio Performance Chart */}
      <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">Portfolio Performance</h2>
          <div className="flex space-x-2">
            <button className="px-3 py-1 bg-white/10 rounded-lg text-white text-sm">1W</button>
            <button className="px-3 py-1 bg-blue-600 rounded-lg text-white text-sm">1M</button>
            <button className="px-3 py-1 bg-white/10 rounded-lg text-white text-sm">3M</button>
            <button className="px-3 py-1 bg-white/10 rounded-lg text-white text-sm">1Y</button>
          </div>
        </div>

        {/* Chart Placeholder */}
        <div className="h-64 bg-white/5 rounded-xl flex items-center justify-center">
          <div className="text-center">
            <i className="fas fa-chart-line text-4xl text-white/40 mb-4"></i>
            <p className="text-white/60">Portfolio performance chart will be displayed here</p>
            <p className="text-white/40 text-sm">Integration with Chart.js coming soon</p>
          </div>
        </div>

        {/* Chart Legend */}
        <div className="flex items-center justify-center space-x-6 mt-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span className="text-white/80 text-sm">Total Balance</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-pink-500 rounded-full mr-2"></div>
            <span className="text-white/80 text-sm">Crypto Assets</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-cyan-500 rounded-full mr-2"></div>
            <span className="text-white/80 text-sm">Tokenized Assets</span>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Your Wallets */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Your Wallets</h2>
            <button className="text-blue-400 hover:text-blue-300 text-sm">View All</button>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fab fa-bitcoin text-white"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Bitcoin Wallet</p>
                  <p className="text-white/60 text-sm">2.5 BTC</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-medium">$125,000</p>
                <p className="text-green-400 text-sm">+5.2%</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fab fa-ethereum text-white"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Ethereum Wallet</p>
                  <p className="text-white/60 text-sm">15.8 ETH</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-medium">$47,400</p>
                <p className="text-red-400 text-sm">-2.1%</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-dollar-sign text-white"></i>
                </div>
                <div>
                  <p className="text-white font-medium">USD Wallet</p>
                  <p className="text-white/60 text-sm">Fiat Currency</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-medium">$10,250</p>
                <p className="text-white/60 text-sm">0.0%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Recent Transactions</h2>
            <button className="text-blue-400 hover:text-blue-300 text-sm">View All</button>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-arrow-down text-white"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Received Bitcoin</p>
                  <p className="text-white/60 text-sm">Dec 28, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-green-400 font-medium">+0.5 BTC</p>
                <p className="text-white/60 text-sm">$25,000</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-arrow-up text-white"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Sent Ethereum</p>
                  <p className="text-white/60 text-sm">Dec 27, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-red-400 font-medium">-2.0 ETH</p>
                <p className="text-white/60 text-sm">$6,000</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-exchange-alt text-white"></i>
                </div>
                <div>
                  <p className="text-white font-medium">USD Deposit</p>
                  <p className="text-white/60 text-sm">Dec 26, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-green-400 font-medium">+$5,000</p>
                <p className="text-white/60 text-sm">Bank Transfer</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-plus text-2xl mb-2"></i>
              <span>Deposit</span>
            </Button>
            <Button className="bg-purple-600 hover:bg-purple-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-exchange-alt text-2xl mb-2"></i>
              <span>Trade</span>
            </Button>
            <Button className="bg-green-600 hover:bg-green-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-paper-plane text-2xl mb-2"></i>
              <span>Send</span>
            </Button>
            <Button className="bg-orange-600 hover:bg-orange-700 text-white p-6 h-auto flex-col">
              <i className="fas fa-chart-line text-2xl mb-2"></i>
              <span>Analytics</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
