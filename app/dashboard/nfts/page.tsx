'use client'

import { useState } from 'react'

export default function NFTsPage() {
  const [activeTab, setActiveTab] = useState('collection')

  const nfts = [
    {
      id: 1,
      name: 'Bored Ape #1234',
      collection: 'Bored Ape Yacht Club',
      image: '/api/placeholder/300/300',
      price: '15.5 ETH',
      usdValue: '$46,500',
      rarity: 'Rare',
      traits: 5,
      lastSale: '14.2 ETH'
    },
    {
      id: 2,
      name: 'CryptoPunk #5678',
      collection: 'CryptoPunks',
      image: '/api/placeholder/300/300',
      price: '25.0 ETH',
      usdValue: '$75,000',
      rarity: 'Ultra Rare',
      traits: 3,
      lastSale: '23.8 ETH'
    },
    {
      id: 3,
      name: 'Azuki #9012',
      collection: 'Azuki',
      image: '/api/placeholder/300/300',
      price: '8.2 ETH',
      usdValue: '$24,600',
      rarity: 'Common',
      traits: 7,
      lastSale: '7.9 ETH'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">NFTs</h1>
          <p className="text-white/60">Manage your NFT collection and marketplace</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-plus mr-2"></i>
            Mint NFT
          </button>
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-store mr-2"></i>
            Marketplace
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['collection', 'marketplace', 'activity'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Collection Tab */}
      {activeTab === 'collection' && (
        <div className="space-y-8">
          {/* Collection Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Total NFTs</h3>
                <i className="fas fa-images text-blue-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">{nfts.length}</p>
              <p className="text-white/60 text-sm">3 collections</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Total Value</h3>
                <i className="fas fa-gem text-purple-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$146,100</p>
              <p className="text-green-400 text-sm">+12.5% this month</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Floor Price</h3>
                <i className="fas fa-chart-line text-green-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">8.2 ETH</p>
              <p className="text-white/60 text-sm">Lowest in collection</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Rarest NFT</h3>
                <i className="fas fa-crown text-yellow-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">Ultra Rare</p>
              <p className="text-white/60 text-sm">CryptoPunk #5678</p>
            </div>
          </div>

          {/* NFT Grid */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Your Collection</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {nfts.map((nft) => (
                <div key={nft.id} className="bg-white/5 rounded-xl overflow-hidden hover:bg-white/10 transition-colors">
                  {/* NFT Image Placeholder */}
                  <div className="aspect-square bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
                    <i className="fas fa-image text-4xl text-white/40"></i>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="text-white font-semibold">{nft.name}</h3>
                        <p className="text-white/60 text-sm">{nft.collection}</p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        nft.rarity === 'Ultra Rare' ? 'bg-yellow-500/20 text-yellow-400' :
                        nft.rarity === 'Rare' ? 'bg-purple-500/20 text-purple-400' :
                        'bg-blue-500/20 text-blue-400'
                      }`}>
                        {nft.rarity}
                      </span>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-white/70">Current Value</span>
                        <span className="text-white font-medium">{nft.price}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/70">USD Value</span>
                        <span className="text-green-400">{nft.usdValue}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/70">Traits</span>
                        <span className="text-white">{nft.traits}</span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-blue-600 hover:bg-blue-700 py-2 rounded-lg text-white text-sm transition-colors">
                        List for Sale
                      </button>
                      <button className="flex-1 bg-white/10 hover:bg-white/20 py-2 rounded-lg text-white text-sm transition-colors">
                        Transfer
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Marketplace Tab */}
      {activeTab === 'marketplace' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">NFT Marketplace</h2>
          <div className="text-center py-12">
            <i className="fas fa-store text-6xl text-white/20 mb-4"></i>
            <p className="text-white/60 text-lg">Marketplace integration coming soon</p>
            <p className="text-white/40">Browse and trade NFTs from major marketplaces</p>
          </div>
        </div>
      )}

      {/* Activity Tab */}
      {activeTab === 'activity' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">NFT Activity</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-shopping-cart text-white"></i>
                </div>
                <div>
                  <p className="text-white font-medium">Purchased Bored Ape #1234</p>
                  <p className="text-white/60 text-sm">Dec 28, 2024 • OpenSea</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-medium">15.5 ETH</p>
                <p className="text-white/60 text-sm">$46,500</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
