'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'

interface User {
  id: string
  email: string
  role: string
  kycStatus: string
  fiatBalance: number
  cryptoHoldings: string
}

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('token')
      
      if (!token) {
        router.push('/auth/login')
        return
      }

      fetchUserData(token)
    }
    
    setTimeout(checkAuth, 100)
  }, [router])

  const fetchUserData = async (token: string) => {
    try {
      const response = await fetch('/api/user/dashboard', {
        headers: {
          'Authorization': `<PERSON><PERSON> ${token}`,
        },
      })

      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      } else {
        localStorage.removeItem('token')
        document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
        router.push('/auth/login')
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      localStorage.removeItem('token')
      document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      router.push('/auth/login')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('token')
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    router.push('/')
  }

  const sidebarItems = [
    { icon: 'fas fa-tachometer-alt', label: 'Dashboard', href: '/dashboard', active: pathname === '/dashboard' },
    { icon: 'fas fa-wallet', label: 'Wallets', href: '/dashboard/wallets', active: pathname === '/dashboard/wallets' },
    { icon: 'fas fa-exchange-alt', label: 'Exchange', href: '/dashboard/exchange', active: pathname === '/dashboard/exchange' },
    { icon: 'fas fa-history', label: 'Transactions', href: '/dashboard/transactions', active: pathname === '/dashboard/transactions' },
    { icon: 'fas fa-chart-line', label: 'Assets', href: '/dashboard/assets', active: pathname === '/dashboard/assets' },
    { icon: 'fas fa-coins', label: 'Staking', href: '/dashboard/staking', active: pathname === '/dashboard/staking' },
    { icon: 'fas fa-cube', label: 'NFTs', href: '/dashboard/nfts', active: pathname === '/dashboard/nfts' },
    { icon: 'fas fa-hand-holding-usd', label: 'Loans', href: '/dashboard/loans', active: pathname === '/dashboard/loans' },
    { icon: 'fas fa-credit-card', label: 'Credit Lines', href: '/dashboard/credit', active: pathname === '/dashboard/credit' },
    { icon: 'fas fa-cog', label: 'Settings', href: '/dashboard/settings', active: pathname === '/dashboard/settings' },
    { icon: 'fas fa-shield-alt', label: 'Security', href: '/dashboard/security', active: pathname === '/dashboard/security' },
    { icon: 'fas fa-headset', label: 'Support', href: '/dashboard/support', active: pathname === '/dashboard/support' },
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="bg-white/10 backdrop-blur-lg p-8 rounded-3xl">
          <i className="fas fa-spinner fa-spin text-4xl text-blue-400 mb-4"></i>
          <p className="text-white/80">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full bg-black/20 backdrop-blur-lg border-r border-white/10 transition-all duration-300 z-50 ${
        sidebarOpen ? 'w-64' : 'w-16'
      }`}>
        {/* Logo */}
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">B</span>
            </div>
            {sidebarOpen && (
              <span className="ml-3 text-xl font-bold text-white">Bank App</span>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-4">
          <div className="space-y-2">
            {sidebarItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className={`flex items-center px-3 py-3 rounded-xl transition-all duration-200 ${
                  item.active 
                    ? 'bg-blue-600 text-white' 
                    : 'text-white/70 hover:bg-white/10 hover:text-white'
                }`}
              >
                <i className={`${item.icon} w-5 text-center`}></i>
                {sidebarOpen && (
                  <span className="ml-3 font-medium">{item.label}</span>
                )}
              </Link>
            ))}
          </div>
        </nav>

        {/* User Profile */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-white/10">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">
                {user.email.charAt(0).toUpperCase()}
              </span>
            </div>
            {sidebarOpen && (
              <div className="ml-3 flex-1">
                <p className="text-white font-medium text-sm truncate">
                  {user.email.split('@')[0]}
                </p>
                <p className="text-white/60 text-xs capitalize">{user.role}</p>
              </div>
            )}
          </div>
          
          {sidebarOpen && (
            <button
              onClick={handleLogout}
              className="w-full mt-3 px-3 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-400 rounded-lg text-sm transition-colors"
            >
              <i className="fas fa-sign-out-alt mr-2"></i>
              Logout
            </button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className={`transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
        {/* Top Bar */}
        <div className="bg-black/10 backdrop-blur-lg border-b border-white/10 p-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="text-white/70 hover:text-white transition-colors"
            >
              <i className="fas fa-bars text-xl"></i>
            </button>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-white font-medium">${user.fiatBalance.toLocaleString()}</p>
                <p className="text-white/60 text-sm">Total Balance</p>
              </div>
              
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {user.email.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <main className="min-h-screen">
          {children}
        </main>
      </div>
    </div>
  )
}
