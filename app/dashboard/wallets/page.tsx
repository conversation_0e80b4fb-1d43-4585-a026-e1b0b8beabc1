'use client'

import { useState } from 'react'

export default function WalletsPage() {
  const [activeTab, setActiveTab] = useState('all')

  const wallets = [
    {
      id: 1,
      name: 'Bitcoin Wallet',
      symbol: 'BTC',
      balance: '2.5',
      usdValue: '$125,000',
      change: '+5.2%',
      changePositive: true,
      icon: 'fab fa-bitcoin',
      color: 'bg-orange-500'
    },
    {
      id: 2,
      name: 'Ethereum Wallet',
      symbol: 'ETH',
      balance: '15.8',
      usdValue: '$47,400',
      change: '-2.1%',
      changePositive: false,
      icon: 'fab fa-ethereum',
      color: 'bg-blue-500'
    },
    {
      id: 3,
      name: 'USD Wallet',
      symbol: 'USD',
      balance: '10,250.00',
      usdValue: '$10,250',
      change: '0.0%',
      changePositive: true,
      icon: 'fas fa-dollar-sign',
      color: 'bg-green-500'
    },
    {
      id: 4,
      name: 'Cardano Wallet',
      symbol: 'ADA',
      balance: '5,000',
      usdValue: '$2,500',
      change: '+8.3%',
      changePositive: true,
      icon: 'fas fa-coins',
      color: 'bg-purple-500'
    },
    {
      id: 5,
      name: 'Solana Wallet',
      symbol: 'SOL',
      balance: '100',
      usdValue: '$8,500',
      change: '+15.7%',
      changePositive: true,
      icon: 'fas fa-sun',
      color: 'bg-indigo-500'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Wallets</h1>
          <p className="text-white/60">Manage your digital assets and wallets</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-plus mr-2"></i>
            Add Wallet
          </button>
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-download mr-2"></i>
            Export
          </button>
        </div>
      </div>

      {/* Wallet Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['all', 'crypto', 'fiat', 'staking'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Total Portfolio Value */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-6 rounded-2xl mb-8 shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-white/90 text-sm mb-2 font-medium">Total Portfolio Value</h2>
            <p className="text-4xl font-bold text-white">$193,650.00</p>
            <p className="text-green-300 text-sm mt-2 font-medium">
              <i className="fas fa-arrow-up mr-1"></i>
              +$8,420 (+4.5%) this month
            </p>
          </div>
          <div className="text-right">
            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
              <i className="fas fa-wallet text-2xl text-white"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Wallets Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {wallets.map((wallet) => (
          <div key={wallet.id} className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-lg rounded-2xl p-6 hover:from-purple-800/40 hover:to-pink-800/40 transition-all duration-300 cursor-pointer border border-white/10 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 bg-gradient-to-r ${
                wallet.symbol === 'BTC' ? 'from-orange-500 to-pink-500' :
                wallet.symbol === 'ETH' ? 'from-cyan-500 to-blue-500' :
                wallet.symbol === 'USD' ? 'from-purple-500 to-indigo-500' :
                wallet.symbol === 'ADA' ? 'from-indigo-500 to-purple-600' :
                'from-yellow-500 to-orange-500'
              } rounded-xl flex items-center justify-center shadow-lg`}>
                <i className={`${wallet.icon} text-white text-xl`}></i>
              </div>
              <button className="text-white/60 hover:text-white transition-colors">
                <i className="fas fa-ellipsis-v"></i>
              </button>
            </div>
            
            <h3 className="text-white font-semibold text-lg mb-2">{wallet.name}</h3>
            <p className="text-white/60 text-sm mb-4">{wallet.symbol}</p>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-white/70 text-sm">Balance</span>
                <span className="text-white font-medium">{wallet.balance} {wallet.symbol}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white/70 text-sm">USD Value</span>
                <span className="text-white font-medium">{wallet.usdValue}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white/70 text-sm">24h Change</span>
                <span className={`font-medium ${wallet.changePositive ? 'text-green-400' : 'text-red-400'}`}>
                  {wallet.change}
                </span>
              </div>
            </div>
            
            <div className="flex space-x-2 mt-4">
              <button className="flex-1 bg-blue-600 hover:bg-blue-700 py-2 rounded-lg text-white text-sm transition-colors">
                Send
              </button>
              <button className="flex-1 bg-white/10 hover:bg-white/20 py-2 rounded-lg text-white text-sm transition-colors">
                Receive
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-gradient-to-br from-cyan-900/30 to-blue-900/30 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
        <h2 className="text-xl font-bold text-white mb-6">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 hover:from-blue-500/30 hover:to-cyan-500/30 p-4 rounded-xl text-center transition-all duration-300 border border-blue-500/30">
            <i className="fas fa-paper-plane text-2xl text-cyan-400 mb-2"></i>
            <p className="text-white font-medium">Send</p>
            <p className="text-white/70 text-sm">Transfer funds</p>
          </button>

          <button className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 p-4 rounded-xl text-center transition-all duration-300 border border-green-500/30">
            <i className="fas fa-qrcode text-2xl text-green-400 mb-2"></i>
            <p className="text-white font-medium">Receive</p>
            <p className="text-white/70 text-sm">Get payments</p>
          </button>

          <button className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 p-4 rounded-xl text-center transition-all duration-300 border border-purple-500/30">
            <i className="fas fa-exchange-alt text-2xl text-purple-400 mb-2"></i>
            <p className="text-white font-medium">Swap</p>
            <p className="text-white/70 text-sm">Exchange assets</p>
          </button>

          <button className="bg-gradient-to-br from-orange-500/20 to-red-500/20 hover:from-orange-500/30 hover:to-red-500/30 p-4 rounded-xl text-center transition-all duration-300 border border-orange-500/30">
            <i className="fas fa-chart-line text-2xl text-orange-400 mb-2"></i>
            <p className="text-white font-medium">Trade</p>
            <p className="text-white/70 text-sm">Buy & sell</p>
          </button>
        </div>
      </div>
    </div>
  )
}
