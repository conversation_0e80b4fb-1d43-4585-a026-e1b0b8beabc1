'use client'

import { useState } from 'react'

export default function TransactionsPage() {
  const [filter, setFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  const transactions = [
    {
      id: 'tx001',
      type: 'received',
      asset: 'BTC',
      amount: '+0.5',
      usdValue: '+$25,000',
      from: '**********************************',
      to: 'Your Bitcoin Wallet',
      date: '2024-12-28',
      time: '14:30',
      status: 'completed',
      fee: '$2.50',
      confirmations: 6,
      hash: '000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f'
    },
    {
      id: 'tx002',
      type: 'sent',
      asset: 'ETH',
      amount: '-2.0',
      usdValue: '-$6,000',
      from: 'Your Ethereum Wallet',
      to: '******************************************',
      date: '2024-12-27',
      time: '09:15',
      status: 'completed',
      fee: '$15.80',
      confirmations: 12,
      hash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef'
    },
    {
      id: 'tx003',
      type: 'deposit',
      asset: 'USD',
      amount: '+$5,000',
      usdValue: '+$5,000',
      from: 'Bank Transfer',
      to: 'Your USD Wallet',
      date: '2024-12-26',
      time: '16:45',
      status: 'completed',
      fee: '$0.00',
      confirmations: 1,
      hash: 'bank_transfer_ref_123456'
    },
    {
      id: 'tx004',
      type: 'swap',
      asset: 'ADA',
      amount: '+1,000',
      usdValue: '+$500',
      from: '0.1 BTC',
      to: 'Your Cardano Wallet',
      date: '2024-12-25',
      time: '11:20',
      status: 'completed',
      fee: '$5.00',
      confirmations: 8,
      hash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890'
    },
    {
      id: 'tx005',
      type: 'withdrawal',
      asset: 'SOL',
      amount: '-50',
      usdValue: '-$4,250',
      from: 'Your Solana Wallet',
      to: 'External Wallet',
      date: '2024-12-24',
      time: '13:10',
      status: 'pending',
      fee: '$0.25',
      confirmations: 2,
      hash: '0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500/20 text-green-400'
      case 'pending': return 'bg-yellow-500/20 text-yellow-400'
      case 'failed': return 'bg-red-500/20 text-red-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'received': return 'fas fa-arrow-down text-green-400'
      case 'sent': return 'fas fa-arrow-up text-red-400'
      case 'deposit': return 'fas fa-plus text-blue-400'
      case 'withdrawal': return 'fas fa-minus text-orange-400'
      case 'swap': return 'fas fa-exchange-alt text-purple-400'
      default: return 'fas fa-circle text-gray-400'
    }
  }

  const filteredTransactions = transactions.filter(tx => {
    const matchesFilter = filter === 'all' || tx.type === filter
    const matchesSearch = tx.asset.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tx.hash.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tx.id.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  })

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Transactions</h1>
          <p className="text-white/60">View and manage your transaction history</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-download mr-2"></i>
            Export CSV
          </button>
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-filter mr-2"></i>
            Advanced Filter
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-white/5 p-1 rounded-xl">
          {['all', 'received', 'sent', 'deposit', 'withdrawal', 'swap'].map((type) => (
            <button
              key={type}
              onClick={() => setFilter(type)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
                filter === type
                  ? 'bg-blue-600 text-white'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              {type}
            </button>
          ))}
        </div>

        {/* Search */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40"></i>
            <input
              type="text"
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Transaction Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-white/80 text-sm">Total Transactions</h3>
            <i className="fas fa-list text-blue-400"></i>
          </div>
          <p className="text-2xl font-bold text-white">1,247</p>
          <p className="text-green-400 text-sm">+23 this week</p>
        </div>

        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-white/80 text-sm">Total Volume</h3>
            <i className="fas fa-chart-bar text-green-400"></i>
          </div>
          <p className="text-2xl font-bold text-white">$2.4M</p>
          <p className="text-green-400 text-sm">+12.5% this month</p>
        </div>

        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-white/80 text-sm">Avg. Transaction</h3>
            <i className="fas fa-calculator text-purple-400"></i>
          </div>
          <p className="text-2xl font-bold text-white">$1,925</p>
          <p className="text-red-400 text-sm">-3.2% this month</p>
        </div>

        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-white/80 text-sm">Total Fees</h3>
            <i className="fas fa-coins text-orange-400"></i>
          </div>
          <p className="text-2xl font-bold text-white">$1,247</p>
          <p className="text-white/60 text-sm">0.05% of volume</p>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white/5 backdrop-blur-lg rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/10">
          <h2 className="text-xl font-bold text-white">Transaction History</h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="text-left p-4 text-white/70 font-medium">Type</th>
                <th className="text-left p-4 text-white/70 font-medium">Asset</th>
                <th className="text-left p-4 text-white/70 font-medium">Amount</th>
                <th className="text-left p-4 text-white/70 font-medium">Date</th>
                <th className="text-left p-4 text-white/70 font-medium">Status</th>
                <th className="text-left p-4 text-white/70 font-medium">Fee</th>
                <th className="text-left p-4 text-white/70 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map((tx) => (
                <tr key={tx.id} className="border-b border-white/5 hover:bg-white/5 transition-colors">
                  <td className="p-4">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center mr-3">
                        <i className={getTypeIcon(tx.type)}></i>
                      </div>
                      <span className="text-white capitalize">{tx.type}</span>
                    </div>
                  </td>
                  <td className="p-4">
                    <div>
                      <p className="text-white font-medium">{tx.asset}</p>
                      <p className="text-white/60 text-sm">{tx.usdValue}</p>
                    </div>
                  </td>
                  <td className="p-4">
                    <div>
                      <p className={`font-medium ${tx.amount.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                        {tx.amount} {tx.asset}
                      </p>
                      <p className="text-white/60 text-sm">{tx.usdValue}</p>
                    </div>
                  </td>
                  <td className="p-4">
                    <div>
                      <p className="text-white">{tx.date}</p>
                      <p className="text-white/60 text-sm">{tx.time}</p>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(tx.status)}`}>
                      {tx.status}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className="text-white">{tx.fee}</span>
                  </td>
                  <td className="p-4">
                    <button className="text-blue-400 hover:text-blue-300 mr-2">
                      <i className="fas fa-eye"></i>
                    </button>
                    <button className="text-white/60 hover:text-white">
                      <i className="fas fa-external-link-alt"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
