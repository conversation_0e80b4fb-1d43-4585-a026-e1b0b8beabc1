'use client'

import { useState } from 'react'

export default function SecurityPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true)

  const securityEvents = [
    {
      event: 'Successful Login',
      location: 'New York, US',
      device: 'Chrome on MacOS',
      time: '2024-12-28 14:30:00',
      ip: '*************',
      status: 'success'
    },
    {
      event: 'Password Changed',
      location: 'New York, US',
      device: 'Chrome on MacOS',
      time: '2024-12-25 09:15:00',
      ip: '*************',
      status: 'success'
    },
    {
      event: 'Failed Login Attempt',
      location: 'Unknown',
      device: 'Unknown Browser',
      time: '2024-12-20 03:45:00',
      ip: '45.123.456.789',
      status: 'failed'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Security</h1>
          <p className="text-white/60">Manage your account security and privacy settings</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-shield-alt mr-2"></i>
            Security Scan
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['overview', 'authentication', 'devices', 'activity'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* Security Score */}
          <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6 rounded-2xl">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-white/80 text-sm mb-2">Security Score</h2>
                <p className="text-4xl font-bold text-white">85/100</p>
                <p className="text-green-200 text-sm mt-2">Good security level</p>
              </div>
              <div className="text-right">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  <i className="fas fa-shield-alt text-2xl text-white"></i>
                </div>
              </div>
            </div>
          </div>

          {/* Security Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <h3 className="text-xl font-bold text-white mb-4">Authentication</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-mobile-alt text-green-400 mr-3"></i>
                    <span className="text-white">Two-Factor Authentication</span>
                  </div>
                  <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Enabled</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-fingerprint text-blue-400 mr-3"></i>
                    <span className="text-white">Biometric Login</span>
                  </div>
                  <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Available</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-key text-purple-400 mr-3"></i>
                    <span className="text-white">Hardware Security Key</span>
                  </div>
                  <span className="bg-gray-500/20 text-gray-400 px-2 py-1 rounded text-xs">Not Set</span>
                </div>
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <h3 className="text-xl font-bold text-white mb-4">Account Protection</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-lock text-green-400 mr-3"></i>
                    <span className="text-white">Strong Password</span>
                  </div>
                  <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Active</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-envelope text-green-400 mr-3"></i>
                    <span className="text-white">Email Verification</span>
                  </div>
                  <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Verified</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="fas fa-phone text-green-400 mr-3"></i>
                    <span className="text-white">Phone Verification</span>
                  </div>
                  <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Verified</span>
                </div>
              </div>
            </div>
          </div>

          {/* Security Recommendations */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h3 className="text-xl font-bold text-white mb-4">Security Recommendations</h3>
            <div className="space-y-3">
              <div className="flex items-center p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <i className="fas fa-exclamation-triangle text-yellow-400 mr-3"></i>
                <div>
                  <p className="text-white font-medium">Enable Hardware Security Key</p>
                  <p className="text-white/60 text-sm">Add an extra layer of security with a hardware key</p>
                </div>
                <button className="ml-auto bg-yellow-600 hover:bg-yellow-700 px-3 py-1 rounded text-white text-sm">
                  Setup
                </button>
              </div>
              
              <div className="flex items-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <i className="fas fa-info-circle text-blue-400 mr-3"></i>
                <div>
                  <p className="text-white font-medium">Review Login Activity</p>
                  <p className="text-white/60 text-sm">Check recent login attempts and devices</p>
                </div>
                <button className="ml-auto bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-white text-sm">
                  Review
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Authentication Tab */}
      {activeTab === 'authentication' && (
        <div className="space-y-8">
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Two-Factor Authentication</h2>
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-white font-medium">Authenticator App</h3>
                <p className="text-white/60 text-sm">Use an authenticator app for secure login</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={twoFactorEnabled}
                  onChange={(e) => setTwoFactorEnabled(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-white/20 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {twoFactorEnabled && (
              <div className="bg-white/5 p-4 rounded-lg">
                <p className="text-green-400 text-sm mb-2">✓ Two-factor authentication is enabled</p>
                <div className="flex space-x-2">
                  <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                    View Backup Codes
                  </button>
                  <button className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                    Disable 2FA
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Login Methods</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                <div className="flex items-center">
                  <i className="fas fa-envelope text-blue-400 mr-3"></i>
                  <div>
                    <p className="text-white font-medium">Email & Password</p>
                    <p className="text-white/60 text-sm">Traditional login method</p>
                  </div>
                </div>
                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Active</span>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                <div className="flex items-center">
                  <i className="fas fa-fingerprint text-purple-400 mr-3"></i>
                  <div>
                    <p className="text-white font-medium">Biometric Authentication</p>
                    <p className="text-white/60 text-sm">Fingerprint or Face ID</p>
                  </div>
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-white text-sm">
                  Setup
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Activity Tab */}
      {activeTab === 'activity' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Security Activity</h2>
          <div className="space-y-4">
            {securityEvents.map((event, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                <div className="flex items-center">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-3 ${
                    event.status === 'success' ? 'bg-green-500' : 'bg-red-500'
                  }`}>
                    <i className={`fas ${
                      event.status === 'success' ? 'fa-check' : 'fa-times'
                    } text-white`}></i>
                  </div>
                  <div>
                    <p className="text-white font-medium">{event.event}</p>
                    <p className="text-white/60 text-sm">{event.location} • {event.device}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white text-sm">{event.time}</p>
                  <p className="text-white/60 text-xs">{event.ip}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
