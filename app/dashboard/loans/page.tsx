'use client'

import { useState } from 'react'

export default function LoansPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [loanAmount, setLoanAmount] = useState('')
  const [loanTerm, setLoanTerm] = useState('12')
  const [collateralType, setCollateralType] = useState('BTC')
  const [showApplication, setShowApplication] = useState(false)

  const activeLoans = [
    {
      id: 'LOAN001',
      amount: '$50,000',
      collateral: '2.5 BTC',
      interestRate: '8.5%',
      term: '24 months',
      monthlyPayment: '$2,284',
      remainingBalance: '$42,150',
      nextPayment: '2025-01-15',
      status: 'active',
      ltv: '65%'
    },
    {
      id: 'LOAN002',
      amount: '$25,000',
      collateral: '10 ETH',
      interestRate: '9.2%',
      term: '12 months',
      monthlyPayment: '$2,185',
      remainingBalance: '$18,750',
      nextPayment: '2025-01-10',
      status: 'active',
      ltv: '70%'
    }
  ]

  const loanHistory = [
    {
      id: 'LOAN003',
      amount: '$30,000',
      collateral: '1.8 BTC',
      status: 'completed',
      completedDate: '2024-11-15',
      totalPaid: '$32,400'
    },
    {
      id: 'LOAN004',
      amount: '$15,000',
      collateral: '5 ETH',
      status: 'defaulted',
      defaultDate: '2024-09-20',
      collateralLiquidated: '5 ETH'
    }
  ]

  const calculateLoanTerms = () => {
    const amount = parseFloat(loanAmount) || 0
    const termMonths = parseInt(loanTerm)
    const interestRate = collateralType === 'BTC' ? 8.5 : collateralType === 'ETH' ? 9.2 : 10.0
    const monthlyRate = interestRate / 100 / 12
    const monthlyPayment = amount * (monthlyRate * Math.pow(1 + monthlyRate, termMonths)) / (Math.pow(1 + monthlyRate, termMonths) - 1)
    
    return {
      interestRate,
      monthlyPayment: monthlyPayment.toFixed(2),
      totalPayment: (monthlyPayment * termMonths).toFixed(2),
      totalInterest: (monthlyPayment * termMonths - amount).toFixed(2)
    }
  }

  const loanTerms = calculateLoanTerms()

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Loans</h1>
          <p className="text-white/60">Manage your crypto-backed loans and credit</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setShowApplication(true)}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors"
          >
            <i className="fas fa-plus mr-2"></i>
            Apply for Loan
          </button>
          <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white transition-colors">
            <i className="fas fa-calculator mr-2"></i>
            Loan Calculator
          </button>
        </div>
      </div>

      {/* Loan Application Modal */}
      {showApplication && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-2xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white">Apply for Crypto Loan</h2>
              <button 
                onClick={() => setShowApplication(false)}
                className="text-white/60 hover:text-white"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>

            <div className="space-y-6">
              {/* Loan Amount */}
              <div>
                <label className="block text-white/70 text-sm mb-2">Loan Amount (USD)</label>
                <input
                  type="number"
                  value={loanAmount}
                  onChange={(e) => setLoanAmount(e.target.value)}
                  placeholder="Enter amount"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-500"
                />
              </div>

              {/* Collateral Type */}
              <div>
                <label className="block text-white/70 text-sm mb-2">Collateral Type</label>
                <select
                  value={collateralType}
                  onChange={(e) => setCollateralType(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="BTC" className="bg-gray-800">Bitcoin (BTC)</option>
                  <option value="ETH" className="bg-gray-800">Ethereum (ETH)</option>
                  <option value="ADA" className="bg-gray-800">Cardano (ADA)</option>
                  <option value="SOL" className="bg-gray-800">Solana (SOL)</option>
                </select>
              </div>

              {/* Loan Term */}
              <div>
                <label className="block text-white/70 text-sm mb-2">Loan Term</label>
                <select
                  value={loanTerm}
                  onChange={(e) => setLoanTerm(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="6" className="bg-gray-800">6 months</option>
                  <option value="12" className="bg-gray-800">12 months</option>
                  <option value="18" className="bg-gray-800">18 months</option>
                  <option value="24" className="bg-gray-800">24 months</option>
                  <option value="36" className="bg-gray-800">36 months</option>
                </select>
              </div>

              {/* Loan Terms Preview */}
              {loanAmount && (
                <div className="bg-white/5 rounded-xl p-4">
                  <h3 className="text-white font-semibold mb-3">Loan Terms Preview</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-white/70">Interest Rate:</span>
                      <span className="text-white font-medium ml-2">{loanTerms.interestRate}% APR</span>
                    </div>
                    <div>
                      <span className="text-white/70">Monthly Payment:</span>
                      <span className="text-white font-medium ml-2">${loanTerms.monthlyPayment}</span>
                    </div>
                    <div>
                      <span className="text-white/70">Total Payment:</span>
                      <span className="text-white font-medium ml-2">${loanTerms.totalPayment}</span>
                    </div>
                    <div>
                      <span className="text-white/70">Total Interest:</span>
                      <span className="text-white font-medium ml-2">${loanTerms.totalInterest}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div className="flex space-x-3">
                <button 
                  onClick={() => setShowApplication(false)}
                  className="flex-1 bg-white/10 hover:bg-white/20 py-3 rounded-lg text-white transition-colors"
                >
                  Cancel
                </button>
                <button className="flex-1 bg-blue-600 hover:bg-blue-700 py-3 rounded-lg text-white transition-colors">
                  Submit Application
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/5 p-1 rounded-xl">
        {['overview', 'active', 'history', 'calculator'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize ${
              activeTab === tab
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* Loan Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Total Borrowed</h3>
                <i className="fas fa-hand-holding-usd text-blue-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$75,000</p>
              <p className="text-green-400 text-sm">2 active loans</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Outstanding Balance</h3>
                <i className="fas fa-balance-scale text-orange-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$60,900</p>
              <p className="text-white/60 text-sm">81.2% of original</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Monthly Payments</h3>
                <i className="fas fa-calendar-alt text-green-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$4,469</p>
              <p className="text-white/60 text-sm">Next: Jan 10, 2025</p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white/80 text-sm">Available Credit</h3>
                <i className="fas fa-credit-card text-purple-400"></i>
              </div>
              <p className="text-2xl font-bold text-white">$125,000</p>
              <p className="text-green-400 text-sm">Based on collateral</p>
            </div>
          </div>

          {/* Active Loans */}
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Active Loans</h2>
            <div className="space-y-4">
              {activeLoans.map((loan) => (
                <div key={loan.id} className="bg-white/5 rounded-xl p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-white font-semibold text-lg">{loan.id}</h3>
                      <p className="text-white/60">Collateral: {loan.collateral}</p>
                    </div>
                    <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-lg text-sm font-medium">
                      Active
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-white/70 text-sm">Loan Amount</p>
                      <p className="text-white font-semibold">{loan.amount}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Interest Rate</p>
                      <p className="text-white font-semibold">{loan.interestRate}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Monthly Payment</p>
                      <p className="text-white font-semibold">{loan.monthlyPayment}</p>
                    </div>
                    <div>
                      <p className="text-white/70 text-sm">Remaining Balance</p>
                      <p className="text-white font-semibold">{loan.remainingBalance}</p>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center mt-4">
                    <div>
                      <p className="text-white/70 text-sm">Next Payment: {loan.nextPayment}</p>
                      <p className="text-white/70 text-sm">LTV Ratio: {loan.ltv}</p>
                    </div>
                    <div className="flex space-x-2">
                      <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                        Make Payment
                      </button>
                      <button className="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg text-white text-sm transition-colors">
                        View Details
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Active Loans Tab */}
      {activeTab === 'active' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Active Loans</h2>
          {/* Same content as overview active loans section */}
        </div>
      )}

      {/* History Tab */}
      {activeTab === 'history' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Loan History</h2>
          <div className="space-y-4">
            {loanHistory.map((loan) => (
              <div key={loan.id} className="bg-white/5 rounded-xl p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-white font-semibold text-lg">{loan.id}</h3>
                    <p className="text-white/60">Amount: {loan.amount}</p>
                    <p className="text-white/60">Collateral: {loan.collateral}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-lg text-sm font-medium ${
                    loan.status === 'completed' 
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-red-500/20 text-red-400'
                  }`}>
                    {loan.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Calculator Tab */}
      {activeTab === 'calculator' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6">
          <h2 className="text-xl font-bold text-white mb-6">Loan Calculator</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <label className="block text-white/70 text-sm mb-2">Loan Amount (USD)</label>
                <input
                  type="number"
                  value={loanAmount}
                  onChange={(e) => setLoanAmount(e.target.value)}
                  placeholder="Enter amount"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">Collateral Type</label>
                <select
                  value={collateralType}
                  onChange={(e) => setCollateralType(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="BTC" className="bg-gray-800">Bitcoin (BTC) - 8.5% APR</option>
                  <option value="ETH" className="bg-gray-800">Ethereum (ETH) - 9.2% APR</option>
                  <option value="ADA" className="bg-gray-800">Cardano (ADA) - 10.0% APR</option>
                </select>
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">Loan Term</label>
                <select
                  value={loanTerm}
                  onChange={(e) => setLoanTerm(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="6" className="bg-gray-800">6 months</option>
                  <option value="12" className="bg-gray-800">12 months</option>
                  <option value="18" className="bg-gray-800">18 months</option>
                  <option value="24" className="bg-gray-800">24 months</option>
                  <option value="36" className="bg-gray-800">36 months</option>
                </select>
              </div>
            </div>

            <div className="bg-white/5 rounded-xl p-6">
              <h3 className="text-white font-semibold mb-4">Calculation Results</h3>
              {loanAmount ? (
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">Loan Amount:</span>
                    <span className="text-white font-medium">${parseFloat(loanAmount).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Interest Rate:</span>
                    <span className="text-white font-medium">{loanTerms.interestRate}% APR</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Loan Term:</span>
                    <span className="text-white font-medium">{loanTerm} months</span>
                  </div>
                  <hr className="border-white/20" />
                  <div className="flex justify-between">
                    <span className="text-white/70">Monthly Payment:</span>
                    <span className="text-white font-bold text-lg">${loanTerms.monthlyPayment}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Total Payment:</span>
                    <span className="text-white font-medium">${loanTerms.totalPayment}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Total Interest:</span>
                    <span className="text-orange-400 font-medium">${loanTerms.totalInterest}</span>
                  </div>
                </div>
              ) : (
                <p className="text-white/60">Enter loan amount to see calculations</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
