'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/lib/hooks/use-toast'

interface AdminStats {
  totalUsers: number
  pendingKyc: number
  totalTransactions: number
  totalVolume: number
}

interface User {
  id: string
  email: string
  role: string
  kycStatus: string
  fiatBalance: number
  createdAt: string
  _count: {
    transactions: number
  }
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/auth/login')
      return
    }

    fetchAdminData(token)
  }, [router])

  const fetchAdminData = async (token: string) => {
    try {
      const [usersResponse] = await Promise.all([
        fetch('/api/admin/users', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ])

      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        setUsers(usersData.users)
        
        // Calculate stats from users data
        const totalUsers = usersData.pagination.total
        const pendingKyc = usersData.users.filter((u: User) => u.kycStatus === 'pending').length
        
        setStats({
          totalUsers,
          pendingKyc,
          totalTransactions: usersData.users.reduce((sum: number, u: User) => sum + u._count.transactions, 0),
          totalVolume: usersData.users.reduce((sum: number, u: User) => sum + u.fiatBalance, 0)
        })
      } else {
        toast({
          title: "Access Denied",
          description: "Admin privileges required",
          variant: "destructive"
        })
        router.push('/dashboard')
      }
    } catch (error) {
      console.error('Error fetching admin data:', error)
      toast({
        title: "Error",
        description: "Failed to load admin data",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleKycAction = async (userId: string, action: 'approved' | 'rejected') => {
    const token = localStorage.getItem('token')
    if (!token) return

    try {
      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          kycStatus: action
        })
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: `KYC ${action} successfully`
        })
        
        // Refresh data
        fetchAdminData(token)
      } else {
        throw new Error('Failed to update KYC status')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update KYC status",
        variant: "destructive"
      })
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="glass-card p-8 rounded-3xl">
          <i className="fas fa-spinner fa-spin text-4xl text-blue-400 mb-4"></i>
          <p className="text-white/80">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-4xl font-bold gradient-text">Admin Dashboard</h1>
          <p className="text-white/70 mt-2">System overview and management</p>
        </div>
        <Button
          onClick={() => router.push('/dashboard')}
          variant="outline"
          className="border-white/20 text-white hover:bg-white/10"
        >
          <i className="fas fa-arrow-left mr-2"></i>
          Back to Dashboard
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="glass-card border-white/10">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <i className="fas fa-users mr-2 text-blue-400"></i>
                Total Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-white">{stats.totalUsers}</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/10">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <i className="fas fa-clock mr-2 text-yellow-400"></i>
                Pending KYC
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-yellow-400">{stats.pendingKyc}</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/10">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <i className="fas fa-exchange-alt mr-2 text-green-400"></i>
                Transactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-white">{stats.totalTransactions}</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/10">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <i className="fas fa-dollar-sign mr-2 text-purple-400"></i>
                Total Volume
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-white">
                ${stats.totalVolume.toLocaleString()}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* KYC Review Section */}
      <Card className="glass-card border-white/10">
        <CardHeader>
          <CardTitle className="text-white">KYC Reviews</CardTitle>
          <CardDescription className="text-white/70">
            Pending user verification requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.filter(user => user.kycStatus === 'pending').map(user => (
              <div key={user.id} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                <div>
                  <p className="text-white font-medium">{user.email}</p>
                  <p className="text-white/60 text-sm">
                    Registered: {new Date(user.createdAt).toLocaleDateString()}
                  </p>
                  <p className="text-white/60 text-sm">
                    Balance: ${user.fiatBalance.toLocaleString()}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={() => handleKycAction(user.id, 'approved')}
                    className="bg-green-600 hover:bg-green-700 text-white"
                    size="sm"
                  >
                    <i className="fas fa-check mr-1"></i>
                    Approve
                  </Button>
                  <Button
                    onClick={() => handleKycAction(user.id, 'rejected')}
                    className="bg-red-600 hover:bg-red-700 text-white"
                    size="sm"
                  >
                    <i className="fas fa-times mr-1"></i>
                    Reject
                  </Button>
                </div>
              </div>
            ))}
            {users.filter(user => user.kycStatus === 'pending').length === 0 && (
              <p className="text-white/60 text-center py-8">No pending KYC reviews</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
