import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function HomePage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="glass-card rounded-3xl p-8 max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-4xl font-bold gradient-text mb-4">
            Bank App
          </h1>
          <p className="text-white/80 text-lg">
            Modern Banking & Crypto Platform
          </p>
        </div>
        
        <div className="space-y-4">
          <Link href="/auth/login" className="block">
            <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 hover-glow">
              <i className="fas fa-sign-in-alt mr-2"></i>
              Sign In
            </Button>
          </Link>
          
          <Link href="/auth/signup" className="block">
            <Button variant="outline" className="w-full border-white/20 text-white hover:bg-white/10 font-semibold py-3 rounded-xl transition-all duration-300">
              <i className="fas fa-user-plus mr-2"></i>
              Create Account
            </Button>
          </Link>
        </div>
        
        <div className="mt-8 pt-6 border-t border-white/10">
          <p className="text-white/60 text-sm">
            Secure • Fast • Reliable
          </p>
        </div>
      </div>
    </div>
  )
}
