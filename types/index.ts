export interface User {
  id: string
  email: string
  role: 'user' | 'admin'
  kycStatus: 'pending' | 'approved' | 'rejected'
  fiatBalance: number
  cryptoHoldings: Record<string, number>
  createdAt: string
  updatedAt: string
}

export interface Transaction {
  id: string
  type: 'fiat-deposit' | 'crypto-buy' | 'crypto-sell' | 'wire' | 'swift' | 'kyc-submission'
  amount: number
  userId: string
  status: 'pending' | 'completed' | 'failed'
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface SignupRequest {
  email: string
  password: string
}

export interface AuthResponse {
  message: string
  token: string
  user: {
    id: string
    email: string
    role: string
    kycStatus: string
  }
}

export interface KYCSubmission {
  documentType: string
  documentNumber: string
  fullName: string
  dateOfBirth?: string
  address?: string
}

export interface FiatDepositRequest {
  amount: number
  bankAccount: string
  routingNumber: string
}

export interface CryptoBuyRequest {
  cryptoSymbol: string
  fiatAmount: number
}

export interface CryptoSellRequest {
  cryptoSymbol: string
  cryptoAmount: number
}

export interface WireTransferRequest {
  amount: number
  recipientName: string
  recipientBank: string
  recipientAccount: string
  swiftCode: string
  purpose: string
}

export interface ApiResponse<T = any> {
  message: string
  data?: T
  error?: string
}

export interface DashboardData {
  user: User
  transactions: Transaction[]
  cryptoPrices?: Record<string, number>
}
