import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    // Get user data
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        role: true,
        kycStatus: true,
        fiatBalance: true,
        cryptoHoldings: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }

    // Get recent transactions
    const transactions = await prisma.transaction.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    res.status(200).json({
      ...user,
      transactions
    })
  } catch (error) {
    console.error('Dashboard error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
