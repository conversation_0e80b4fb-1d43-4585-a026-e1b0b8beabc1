import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    // Check if user is admin
    if (payload.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' })
    }

    // Get query parameters
    const page = parseInt(req.query.page as string) || 1
    const limit = parseInt(req.query.limit as string) || 20
    const skip = (page - 1) * limit
    const type = req.query.type as string
    const status = req.query.status as string
    const userId = req.query.userId as string

    // Build where clause
    const where: any = {}
    if (type) where.type = type
    if (status) where.status = status
    if (userId) where.userId = userId

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              role: true,
              kycStatus: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.transaction.count({ where })
    ])

    // Get transaction statistics
    const stats = await prisma.transaction.groupBy({
      by: ['type', 'status'],
      _count: {
        id: true
      },
      _sum: {
        amount: true
      }
    })

    res.status(200).json({
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats
    })
  } catch (error) {
    console.error('Admin transactions error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
