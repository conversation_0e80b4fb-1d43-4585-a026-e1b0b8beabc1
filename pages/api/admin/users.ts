import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    // Check if user is admin
    if (payload.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' })
    }

    if (req.method === 'GET') {
      // Get all users with pagination
      const page = parseInt(req.query.page as string) || 1
      const limit = parseInt(req.query.limit as string) || 10
      const skip = (page - 1) * limit

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          skip,
          take: limit,
          select: {
            id: true,
            email: true,
            role: true,
            kycStatus: true,
            fiatBalance: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                transactions: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        prisma.user.count()
      ])

      res.status(200).json({
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      })
    } else if (req.method === 'PUT') {
      // Update user (e.g., approve/reject KYC)
      const { userId, kycStatus, role } = req.body

      if (!userId) {
        return res.status(400).json({ message: 'User ID required' })
      }

      const updateData: any = {}
      if (kycStatus) updateData.kycStatus = kycStatus
      if (role) updateData.role = role

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          role: true,
          kycStatus: true,
          fiatBalance: true,
          createdAt: true,
          updatedAt: true
        }
      })

      // Create audit log
      await prisma.transaction.create({
        data: {
          type: 'admin-action',
          amount: 0,
          userId: updatedUser.id,
          status: 'completed',
          metadata: {
            action: 'user_updated',
            adminId: payload.userId,
            changes: updateData,
            timestamp: new Date().toISOString()
          }
        }
      })

      res.status(200).json({
        message: 'User updated successfully',
        user: updatedUser
      })
    } else {
      res.status(405).json({ message: 'Method not allowed' })
    }
  } catch (error) {
    console.error('Admin users error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
