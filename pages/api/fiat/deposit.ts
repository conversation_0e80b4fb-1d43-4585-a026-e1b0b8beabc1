import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    const { amount, bankAccount, routingNumber } = req.body

    if (!amount || amount <= 0) {
      return res.status(400).json({ message: 'Invalid amount' })
    }

    if (!bankAccount || !routingNumber) {
      return res.status(400).json({ message: 'Bank details required' })
    }

    // Check user KYC status
    const user = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }

    if (user.kycStatus !== 'approved') {
      return res.status(403).json({ message: 'KYC approval required for deposits' })
    }

    // Create transaction record
    const transaction = await prisma.transaction.create({
      data: {
        type: 'fiat-deposit',
        amount: parseFloat(amount),
        userId: payload.userId,
        status: 'pending',
        metadata: JSON.stringify({
          bankAccount: bankAccount.slice(-4), // Only store last 4 digits
          routingNumber,
          initiatedAt: new Date().toISOString()
        })
      }
    })

    // Simulate bank transfer processing (in real app, this would be async)
    setTimeout(async () => {
      try {
        // Update transaction status and user balance
        await prisma.$transaction([
          prisma.transaction.update({
            where: { id: transaction.id },
            data: { status: 'completed' }
          }),
          prisma.user.update({
            where: { id: payload.userId },
            data: {
              fiatBalance: {
                increment: parseFloat(amount)
              }
            }
          })
        ])
      } catch (error) {
        console.error('Error processing deposit:', error)
      }
    }, 5000) // 5 second delay to simulate processing

    res.status(200).json({
      message: 'Deposit initiated successfully',
      transactionId: transaction.id,
      status: 'pending'
    })
  } catch (error) {
    console.error('Deposit error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
