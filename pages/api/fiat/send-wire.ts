import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    const { 
      amount, 
      recipientName, 
      recipientBank, 
      recipientAccount, 
      swiftCode, 
      purpose 
    } = req.body

    if (!amount || amount <= 0) {
      return res.status(400).json({ message: 'Invalid amount' })
    }

    if (!recipientName || !recipientBank || !recipientAccount || !swiftCode) {
      return res.status(400).json({ message: 'All recipient details required' })
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }

    if (user.kycStatus !== 'approved') {
      return res.status(403).json({ message: 'KYC approval required for wire transfers' })
    }

    if (user.fiatBalance < amount) {
      return res.status(400).json({ message: 'Insufficient balance' })
    }

    // Wire transfer fee (2% or minimum $25)
    const fee = Math.max(amount * 0.02, 25)
    const totalAmount = amount + fee

    if (user.fiatBalance < totalAmount) {
      return res.status(400).json({ message: 'Insufficient balance including fees' })
    }

    // Create transaction and update balance
    const transaction = await prisma.$transaction([
      prisma.transaction.create({
        data: {
          type: 'wire',
          amount: totalAmount,
          userId: payload.userId,
          status: 'pending',
          metadata: JSON.stringify({
            recipientName,
            recipientBank,
            recipientAccount: recipientAccount.slice(-4), // Only store last 4 digits
            swiftCode,
            purpose,
            wireAmount: amount,
            fee,
            initiatedAt: new Date().toISOString()
          })
        }
      }),
      prisma.user.update({
        where: { id: payload.userId },
        data: {
          fiatBalance: {
            decrement: totalAmount
          }
        }
      })
    ])

    // Simulate wire processing (in real app, this would integrate with SWIFT network)
    setTimeout(async () => {
      try {
        await prisma.transaction.update({
          where: { id: transaction[0].id },
          data: { status: 'completed' }
        })
      } catch (error) {
        console.error('Error processing wire transfer:', error)
      }
    }, 10000) // 10 second delay to simulate processing

    res.status(200).json({
      message: 'Wire transfer initiated successfully',
      transactionId: transaction[0].id,
      amount,
      fee,
      totalAmount,
      status: 'pending'
    })
  } catch (error) {
    console.error('Wire transfer error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
