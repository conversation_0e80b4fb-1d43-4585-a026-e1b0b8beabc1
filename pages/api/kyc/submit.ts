import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    const { documentType, documentNumber, fullName, dateOfBirth, address } = req.body

    if (!documentType || !documentNumber || !fullName) {
      return res.status(400).json({ message: 'Required fields missing' })
    }

    // Update user KYC status to pending
    const user = await prisma.user.update({
      where: { id: payload.userId },
      data: {
        kycStatus: 'pending'
      }
    })

    // Create transaction record for KYC submission
    await prisma.transaction.create({
      data: {
        type: 'kyc-submission',
        amount: 0,
        userId: payload.userId,
        status: 'pending',
        metadata: {
          documentType,
          documentNumber,
          fullName,
          dateOfBirth,
          address,
          submittedAt: new Date().toISOString()
        }
      }
    })

    res.status(200).json({
      message: 'KYC documents submitted successfully',
      kycStatus: user.kycStatus
    })
  } catch (error) {
    console.error('KYC submission error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
