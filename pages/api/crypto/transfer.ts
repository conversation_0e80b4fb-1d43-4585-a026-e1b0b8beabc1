import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    const { cryptoSymbol, cryptoAmount, toAddress, network } = req.body

    if (!cryptoSymbol || !cryptoAmount || cryptoAmount <= 0 || !toAddress || !network) {
      return res.status(400).json({ message: 'All fields required' })
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }

    if (user.kycStatus !== 'approved') {
      return res.status(403).json({ message: 'KYC approval required for crypto transfers' })
    }

    // Check crypto holdings
    const currentHoldings = user.cryptoHoldings as any || {}
    const currentAmount = currentHoldings[cryptoSymbol] || 0

    if (currentAmount < cryptoAmount) {
      return res.status(400).json({ message: 'Insufficient crypto balance' })
    }

    // Network fees (simplified)
    const networkFees: { [key: string]: number } = {
      'ethereum': 0.005, // ETH
      'bitcoin': 0.0001, // BTC
      'solana': 0.00025, // SOL
    }

    const networkFee = networkFees[network.toLowerCase()] || 0.001

    if (currentAmount < cryptoAmount + networkFee) {
      return res.status(400).json({ message: 'Insufficient balance including network fees' })
    }

    // Update user holdings
    const newHoldings = {
      ...currentHoldings,
      [cryptoSymbol]: currentAmount - cryptoAmount - networkFee
    }

    // Remove crypto if balance becomes 0
    if (newHoldings[cryptoSymbol] <= 0) {
      delete newHoldings[cryptoSymbol]
    }

    // Generate mock transaction hash
    const txHash = `0x${Math.random().toString(16).substr(2, 64)}`

    await prisma.$transaction([
      // Create transaction record
      prisma.transaction.create({
        data: {
          type: 'crypto-transfer',
          amount: cryptoAmount,
          userId: payload.userId,
          status: 'pending',
          metadata: {
            cryptoSymbol,
            cryptoAmount,
            toAddress,
            network,
            networkFee,
            txHash,
            initiatedAt: new Date().toISOString()
          }
        }
      }),
      // Update user holdings
      prisma.user.update({
        where: { id: payload.userId },
        data: {
          cryptoHoldings: newHoldings
        }
      })
    ])

    // Simulate blockchain confirmation (in real app, this would monitor actual blockchain)
    setTimeout(async () => {
      try {
        await prisma.transaction.updateMany({
          where: { 
            userId: payload.userId,
            type: 'crypto-transfer',
            status: 'pending'
          },
          data: { status: 'completed' }
        })
      } catch (error) {
        console.error('Error confirming crypto transfer:', error)
      }
    }, 30000) // 30 second delay to simulate blockchain confirmation

    res.status(200).json({
      message: 'Crypto transfer initiated successfully',
      txHash,
      cryptoSymbol,
      cryptoAmount,
      toAddress,
      network,
      networkFee,
      status: 'pending'
    })
  } catch (error) {
    console.error('Crypto transfer error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
