import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { verifyToken, extractTokenFromHeader } from '@/lib/auth'

// Mock crypto prices (in real app, fetch from API like CoinGecko)
const CRYPTO_PRICES: { [key: string]: number } = {
  'BTC': 45000,
  'ETH': 3000,
  'USDC': 1,
  'SOL': 100,
  'ADA': 0.5
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Extract and verify token
    const token = extractTokenFromHeader(req.headers.authorization)
    if (!token) {
      return res.status(401).json({ message: 'No token provided' })
    }

    const payload = verifyToken(token)
    if (!payload) {
      return res.status(401).json({ message: 'Invalid token' })
    }

    const { cryptoSymbol, fiatAmount } = req.body

    if (!cryptoSymbol || !fiatAmount || fiatAmount <= 0) {
      return res.status(400).json({ message: 'Invalid parameters' })
    }

    if (!CRYPTO_PRICES[cryptoSymbol]) {
      return res.status(400).json({ message: 'Unsupported cryptocurrency' })
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }

    if (user.kycStatus !== 'approved') {
      return res.status(403).json({ message: 'KYC approval required for crypto trading' })
    }

    if (user.fiatBalance < fiatAmount) {
      return res.status(400).json({ message: 'Insufficient fiat balance' })
    }

    // Calculate crypto amount
    const cryptoPrice = CRYPTO_PRICES[cryptoSymbol]
    const cryptoAmount = fiatAmount / cryptoPrice

    // Update user balances
    const currentHoldings = JSON.parse(user.cryptoHoldings || '{}')
    const newHoldings = {
      ...currentHoldings,
      [cryptoSymbol]: (currentHoldings[cryptoSymbol] || 0) + cryptoAmount
    }

    await prisma.$transaction([
      // Create transaction record
      prisma.transaction.create({
        data: {
          type: 'crypto-buy',
          amount: fiatAmount,
          userId: payload.userId,
          status: 'completed',
          metadata: JSON.stringify({
            cryptoSymbol,
            cryptoAmount,
            cryptoPrice,
            executedAt: new Date().toISOString()
          })
        }
      }),
      // Update user balances
      prisma.user.update({
        where: { id: payload.userId },
        data: {
          fiatBalance: {
            decrement: fiatAmount
          },
          cryptoHoldings: JSON.stringify(newHoldings)
        }
      })
    ])

    res.status(200).json({
      message: 'Crypto purchase successful',
      cryptoSymbol,
      cryptoAmount,
      fiatAmount,
      price: cryptoPrice
    })
  } catch (error) {
    console.error('Crypto buy error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
