import { create } from 'zustand'

interface Transaction {
  id: string
  type: string
  amount: number
  status: string
  createdAt: string
  metadata?: any
}

interface AppState {
  transactions: Transaction[]
  isLoading: boolean
  error: string | null
  setTransactions: (transactions: Transaction[]) => void
  addTransaction: (transaction: Transaction) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
}

export const useAppStore = create<AppState>((set, get) => ({
  transactions: [],
  isLoading: false,
  error: null,
  
  setTransactions: (transactions: Transaction[]) => {
    set({ transactions })
  },
  
  addTransaction: (transaction: Transaction) => {
    const currentTransactions = get().transactions
    set({
      transactions: [transaction, ...currentTransactions]
    })
  },
  
  setLoading: (isLoading: boolean) => {
    set({ isLoading })
  },
  
  setError: (error: string | null) => {
    set({ error })
  },
  
  clearError: () => {
    set({ error: null })
  }
}))
